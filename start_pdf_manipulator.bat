@echo off
echo PDF Manipulator Pro Launcher
echo ============================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "pdf_manipulator_pro.py" (
    echo Error: pdf_manipulator_pro.py not found
    echo Please ensure you're running this from the correct directory
    pause
    exit /b 1
)

REM Launch the application
echo Starting PDF Manipulator Pro...
python pdf_manipulator_pro.py

echo.
echo PDF Manipulator Pro session ended.
pause
