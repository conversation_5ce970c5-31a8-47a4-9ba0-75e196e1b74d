@echo off
echo PDF Manipulator Pro Launcher
echo ============================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "modern_main.py" (
    if not exist "main.py" (
        echo Error: No application files found
        echo Please ensure you're running this from the correct directory
        pause
        exit /b 1
    )
)

REM Launch the application
echo Starting PDF Manipulator Pro...
python pdf_manipulator_pro.py

REM If main launcher fails, try alternatives
if errorlevel 1 (
    echo.
    echo Main launcher failed, trying alternatives...
    if exist "launch_pdf_manipulator.py" (
        echo Trying alternative launcher...
        python launch_pdf_manipulator.py
    ) else if exist "modern_main.py" (
        echo Starting Modern UI directly...
        python modern_main.py
    ) else (
        echo No application files found
        pause
        exit /b 1
    )
)

echo.
echo PDF Manipulator Pro session ended.
