@echo off
echo PDF Manipulator Pro Launcher
echo ============================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "modern_main.py" (
    if not exist "main.py" (
        echo Error: No application files found
        echo Please ensure you're running this from the correct directory
        pause
        exit /b 1
    )
)

REM Launch the application launcher
echo Starting PDF Manipulator Pro...
python launch_app.py

REM If launcher fails, try direct launch
if errorlevel 1 (
    echo.
    echo Launcher failed, trying direct launch...
    if exist "modern_main.py" (
        echo Starting Modern UI...
        python modern_main.py
    ) else if exist "main.py" (
        echo Starting Classic UI...
        python main.py
    ) else (
        echo No application files found
        pause
        exit /b 1
    )
)

echo.
echo PDF Manipulator Pro session ended.
