"""
Simple test script to verify the welcome screen works.
"""

import tkinter as tk
import customtkinter as ctk
from welcome_screen import show_welcome_screen


def test_welcome_screen():
    """Test the welcome screen functionality."""
    print("Testing welcome screen...")
    
    # Create a dummy root window
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    def start_callback():
        print("Start button clicked!")
        root.quit()
    
    # Show welcome screen
    try:
        welcome = show_welcome_screen(root, start_callback)
        print("Welcome screen created successfully!")
        
        # Run the main loop
        root.mainloop()
        
    except Exception as e:
        print(f"Error creating welcome screen: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            root.destroy()
        except:
            pass


def test_modern_app_direct():
    """Test launching the modern app directly."""
    print("Testing modern app launch...")
    
    try:
        from modern_main import ModernPDFManipulatorApp
        
        # Create app without welcome screen
        app = ModernPDFManipulatorApp(show_welcome=False)
        print("Modern app created successfully!")
        
        # Don't run mainloop in test
        app.root.destroy()
        
    except Exception as e:
        print(f"Error creating modern app: {e}")
        import traceback
        traceback.print_exc()


def test_logo_creation():
    """Test logo file existence."""
    print("Testing logo files...")
    
    import os
    
    files_to_check = ["logo.png", "icon.png", "icon.ico", "banner.png"]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
    
    # Try to create logos if missing
    if not os.path.exists("logo.png"):
        print("Creating missing logo files...")
        try:
            from create_logo import main as create_logos
            create_logos()
            print("Logo files created successfully!")
        except Exception as e:
            print(f"Error creating logos: {e}")


def main():
    """Run all tests."""
    print("PDF Manipulator Welcome Screen Tests")
    print("=" * 40)
    
    # Test 1: Logo files
    test_logo_creation()
    print()
    
    # Test 2: Modern app direct
    test_modern_app_direct()
    print()
    
    # Test 3: Welcome screen (interactive)
    response = input("Do you want to test the welcome screen interactively? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        test_welcome_screen()
    
    print("\nTests completed!")


if __name__ == "__main__":
    main()
