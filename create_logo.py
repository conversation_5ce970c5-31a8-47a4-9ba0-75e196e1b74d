"""
<PERSON><PERSON><PERSON> to create a professional logo for PDF Manipulator Pro.
"""

from PIL import Image, ImageDraw, ImageFont
import os


def create_app_logo():
    """Create a professional logo for the application."""
    # Logo dimensions
    size = (256, 256)
    logo = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(logo)
    
    # Colors
    primary_blue = "#1f538d"
    accent_orange = "#ff6b35"
    white = "#ffffff"
    light_gray = "#f0f0f0"
    
    # Background circle
    center = (128, 128)
    radius = 120
    draw.ellipse([center[0] - radius, center[1] - radius,
                 center[0] + radius, center[1] + radius],
                fill=primary_blue, outline=white, width=4)
    
    # Main document shape
    doc_width = 80
    doc_height = 100
    doc_x = center[0] - doc_width // 2
    doc_y = center[1] - doc_height // 2
    
    # Document background
    draw.rounded_rectangle([doc_x, doc_y, doc_x + doc_width, doc_y + doc_height],
                          radius=8, fill=white, outline=light_gray, width=2)
    
    # Document lines (text representation)
    line_color = primary_blue
    line_width = 2
    for i, y_offset in enumerate([15, 25, 35, 45, 55, 65]):
        line_length = 50 if i % 2 == 0 else 35
        start_x = doc_x + 15
        line_y = doc_y + y_offset
        draw.rectangle([start_x, line_y, start_x + line_length, line_y + line_width],
                      fill=line_color)
    
    # Corner fold effect
    fold_size = 15
    fold_points = [
        (doc_x + doc_width - fold_size, doc_y),
        (doc_x + doc_width, doc_y),
        (doc_x + doc_width, doc_y + fold_size),
        (doc_x + doc_width - fold_size, doc_y)
    ]
    draw.polygon(fold_points, fill=light_gray, outline=primary_blue, width=1)
    
    # Gear/settings overlay (representing manipulation/processing)
    gear_center = (center[0] + 35, center[1] - 35)
    gear_radius = 20
    
    # Gear background
    draw.ellipse([gear_center[0] - gear_radius, gear_center[1] - gear_radius,
                 gear_center[0] + gear_radius, gear_center[1] + gear_radius],
                fill=accent_orange, outline=white, width=3)
    
    # Gear teeth (simplified)
    for angle in range(0, 360, 45):
        import math
        x = gear_center[0] + (gear_radius + 5) * math.cos(math.radians(angle))
        y = gear_center[1] + (gear_radius + 5) * math.sin(math.radians(angle))
        draw.ellipse([x - 3, y - 3, x + 3, y + 3], fill=accent_orange)
    
    # Inner gear circle
    inner_radius = 8
    draw.ellipse([gear_center[0] - inner_radius, gear_center[1] - inner_radius,
                 gear_center[0] + inner_radius, gear_center[1] + inner_radius],
                fill=white, outline=accent_orange, width=2)
    
    # Save logo
    logo.save("logo.png", "PNG")
    print("Logo saved as logo.png")
    
    # Create smaller versions
    # Icon size (for window icon)
    icon = logo.resize((64, 64), Image.Resampling.LANCZOS)
    icon.save("icon.png", "PNG")
    print("Icon saved as icon.png")
    
    # Try to create ICO file for Windows
    try:
        icon.save("icon.ico", "ICO")
        print("Windows icon saved as icon.ico")
    except Exception as e:
        print(f"Could not create ICO file: {e}")
    
    return logo


def create_banner_logo():
    """Create a horizontal banner logo."""
    # Banner dimensions
    width, height = 400, 120
    banner = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(banner)
    
    # Colors
    primary_blue = "#1f538d"
    accent_orange = "#ff6b35"
    white = "#ffffff"
    
    # Logo section (left side)
    logo_size = 80
    logo_x = 20
    logo_y = (height - logo_size) // 2
    
    # Mini document
    doc_width = 30
    doc_height = 40
    doc_x = logo_x + (logo_size - doc_width) // 2
    doc_y = logo_y + (logo_size - doc_height) // 2
    
    draw.rounded_rectangle([doc_x, doc_y, doc_x + doc_width, doc_y + doc_height],
                          radius=4, fill=primary_blue, outline=white, width=1)
    
    # Document lines
    for i, y_offset in enumerate([8, 14, 20, 26]):
        line_length = 18 if i % 2 == 0 else 12
        start_x = doc_x + 6
        line_y = doc_y + y_offset
        draw.rectangle([start_x, line_y, start_x + line_length, line_y + 1],
                      fill=white)
    
    # Gear overlay
    gear_x = logo_x + 50
    gear_y = logo_y + 15
    gear_radius = 12
    draw.ellipse([gear_x - gear_radius, gear_y - gear_radius,
                 gear_x + gear_radius, gear_y + gear_radius],
                fill=accent_orange, outline=white, width=2)
    
    # Text section (right side)
    text_x = logo_x + logo_size + 20
    text_y = logo_y
    
    try:
        # Try to use a nice font
        title_font = ImageFont.truetype("arial.ttf", 24)
        subtitle_font = ImageFont.truetype("arial.ttf", 14)
    except:
        # Fallback to default font
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
    
    # Title text
    draw.text((text_x, text_y + 10), "PDF Manipulator", fill=primary_blue, font=title_font)
    draw.text((text_x, text_y + 40), "Professional PDF Processing", fill="#666666", font=subtitle_font)
    
    # Save banner
    banner.save("banner.png", "PNG")
    print("Banner saved as banner.png")
    
    return banner


def main():
    """Create all logo variants."""
    print("Creating PDF Manipulator Pro logos...")
    
    # Create main logo
    logo = create_app_logo()
    
    # Create banner
    banner = create_banner_logo()
    
    print("\nLogo creation complete!")
    print("Files created:")
    print("- logo.png (256x256 main logo)")
    print("- icon.png (64x64 icon)")
    print("- icon.ico (Windows icon, if supported)")
    print("- banner.png (400x120 banner)")


if __name__ == "__main__":
    main()
