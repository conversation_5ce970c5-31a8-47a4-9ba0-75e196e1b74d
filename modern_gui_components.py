"""
Modern GUI components using CustomTkinter for the PDF manipulation application.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinterdnd2 import DND_FILES, TkinterDnD
from typing import List, Callable, Optional
from PIL import Image, ImageTk
import threading
import os


# Set appearance mode and color theme
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"


class ModernDragDropFrame(ctk.CTkFrame):
    """Modern drag and drop frame with contemporary styling."""

    def __init__(self, parent, drop_callback: Callable[[List[str]], None], **kwargs):
        super().__init__(parent, **kwargs)
        self.drop_callback = drop_callback

        # Configure drag and drop
        self.drop_target_register(DND_FILES)
        self.dnd_bind('<<Drop>>', self._on_drop)

        # Create main container
        self.main_frame = ctk.CTkFrame(self, corner_radius=15, border_width=2)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Icon and text
        self.icon_label = ctk.CTkLabel(
            self.main_frame,
            text="📁",
            font=ctk.CTkFont(size=48)
        )
        self.icon_label.pack(pady=(20, 10))

        self.text_label = ctk.CTkLabel(
            self.main_frame,
            text="Drag and drop PDF files here\nor click to browse",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        self.text_label.pack(pady=(0, 10))

        self.browse_button = ctk.CTkButton(
            self.main_frame,
            text="Browse Files",
            command=self._on_click,
            width=120,
            height=32,
            corner_radius=8
        )
        self.browse_button.pack(pady=(0, 20))

        # Bind click events
        self.bind("<Button-1>", self._on_click)
        self.main_frame.bind("<Button-1>", self._on_click)
        self.icon_label.bind("<Button-1>", self._on_click)
        self.text_label.bind("<Button-1>", self._on_click)

    def _on_drop(self, event):
        """Handle file drop event."""
        files = self.tk.splitlist(event.data)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        if pdf_files:
            self.drop_callback(pdf_files)

    def _on_click(self, event=None):
        """Handle click event to browse files."""
        from utils import select_input_files
        files = select_input_files()
        if files:
            self.drop_callback(files)


class ModernProgressDialog(ctk.CTkToplevel):
    """Modern progress dialog with contemporary styling."""

    def __init__(self, parent, title: str = "Processing..."):
        super().__init__(parent)
        self.title(title)
        self.geometry("450x180")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()

        # Center the dialog
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.cancelled = False
        self._destroyed = False

        # Create widgets
        self._create_widgets(title)

        # Bind close event
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)

    def _create_widgets(self, title):
        """Create dialog widgets."""
        # Main container
        self.main_frame = ctk.CTkFrame(self, corner_radius=15)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text=title,
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))

        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(
            self.main_frame,
            width=350,
            height=20,
            corner_radius=10
        )
        self.progress_bar.pack(pady=10)
        self.progress_bar.set(0)

        # Status label
        self.status_text = "Initializing..."
        self.status_label = ctk.CTkLabel(
            self.main_frame,
            text=self.status_text,
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40")
        )
        self.status_label.pack(pady=5)

        # Cancel button
        self.cancel_button = ctk.CTkButton(
            self.main_frame,
            text="Cancel",
            command=self._on_cancel,
            width=100,
            height=32,
            corner_radius=8,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        self.cancel_button.pack(pady=(10, 20))

    def update_progress(self, progress: float, status: str = ""):
        """Update progress bar and status."""
        if self._destroyed or self.cancelled:
            return

        try:
            if self.winfo_exists():
                self.progress_bar.set(progress / 100.0)
                if status:
                    self.status_text = status
                    self.status_label.configure(text=status)
                self.update_idletasks()
        except Exception:
            # Dialog may have been destroyed
            self._destroyed = True

    def _on_cancel(self):
        """Handle cancel button click."""
        self.cancelled = True
        self.safe_destroy()

    def safe_destroy(self):
        """Safely destroy the dialog."""
        if self._destroyed:
            return

        self.cancelled = True
        self._destroyed = True

        try:
            # Disable all widgets first
            if hasattr(self, 'cancel_button'):
                self.cancel_button.configure(state="disabled")
            if hasattr(self, 'progress_bar'):
                self.progress_bar.configure(state="disabled")

            # Schedule destruction
            self.after_idle(self._do_destroy)
        except Exception:
            pass

    def _do_destroy(self):
        """Actually destroy the dialog."""
        try:
            if self.winfo_exists():
                self.destroy()
        except Exception:
            pass


class ModernPageThumbnailFrame(ctk.CTkScrollableFrame):
    """Modern scrollable frame for displaying PDF page thumbnails."""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        self.thumbnails = []
        self.selected_pages = set()
        self.selection_callback = None

        # Configure grid
        self.grid_columnconfigure(0, weight=1)

    def set_selection_callback(self, callback: Callable[[set], None]):
        """Set callback for page selection changes."""
        self.selection_callback = callback

    def clear_thumbnails(self):
        """Clear all thumbnails."""
        for widget in self.winfo_children():
            widget.destroy()
        self.thumbnails.clear()
        self.selected_pages.clear()

    def add_thumbnail(self, page_num: int, image: Image.Image, selectable: bool = True):
        """Add a thumbnail to the frame."""
        # Resize image to thumbnail size
        image.thumbnail((150, 200), Image.Resampling.LANCZOS)

        # Convert to CTkImage for proper scaling
        ctk_image = ctk.CTkImage(light_image=image, dark_image=image, size=(150, 200))

        # Create frame for thumbnail
        thumb_frame = ctk.CTkFrame(self, corner_radius=10)
        thumb_frame.grid(row=page_num, column=0, pady=10, padx=10, sticky="ew")

        # Page number label
        page_label = ctk.CTkLabel(
            thumb_frame,
            text=f"Page {page_num + 1}",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        page_label.pack(pady=(10, 5))

        # Thumbnail image
        img_label = ctk.CTkLabel(thumb_frame, image=ctk_image, text="")
        img_label.pack(pady=5)

        if selectable:
            # Checkbox for selection
            var = ctk.BooleanVar()
            checkbox = ctk.CTkCheckBox(
                thumb_frame,
                text="Select",
                variable=var,
                command=lambda: self._on_selection_change(page_num, var.get()),
                corner_radius=5
            )
            checkbox.pack(pady=(5, 10))

            self.thumbnails.append({
                'page_num': page_num,
                'frame': thumb_frame,
                'var': var,
                'checkbox': checkbox
            })

    def _on_selection_change(self, page_num: int, selected: bool):
        """Handle page selection change."""
        if selected:
            self.selected_pages.add(page_num)
        else:
            self.selected_pages.discard(page_num)

        if self.selection_callback:
            self.selection_callback(self.selected_pages)

    def select_all(self):
        """Select all pages."""
        for thumb in self.thumbnails:
            thumb['var'].set(True)
            self.selected_pages.add(thumb['page_num'])

        if self.selection_callback:
            self.selection_callback(self.selected_pages)

    def select_none(self):
        """Deselect all pages."""
        for thumb in self.thumbnails:
            thumb['var'].set(False)
        self.selected_pages.clear()

        if self.selection_callback:
            self.selection_callback(self.selected_pages)


class ModernFileListFrame(ctk.CTkFrame):
    """Modern frame for displaying and managing a list of files."""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # Create scrollable text widget for file list
        self.textbox = ctk.CTkTextbox(
            self,
            corner_radius=10,
            border_width=1,
            font=ctk.CTkFont(size=12)
        )
        self.textbox.pack(fill="both", expand=True, padx=10, pady=10)

        # Store file paths
        self.file_paths = []
        self.selection_callback = None
        self.double_click_callback = None

        # Bind events
        self.textbox.bind("<Double-Button-1>", self._on_double_click)

    def set_selection_callback(self, callback: Callable[[List[str]], None]):
        """Set callback for selection changes."""
        self.selection_callback = callback

    def set_double_click_callback(self, callback: Callable[[str], None]):
        """Set callback for double-click events."""
        self.double_click_callback = callback

    def add_file(self, file_path: str):
        """Add a file to the list."""
        self.file_paths.append(file_path)
        self._update_display()

    def add_files(self, file_paths: List[str]):
        """Add multiple files to the list."""
        self.file_paths.extend(file_paths)
        self._update_display()

    def remove_selected(self):
        """Remove selected files from the list (simplified for demo)."""
        # For now, remove the last file
        if self.file_paths:
            self.file_paths.pop()
            self._update_display()

    def clear_all(self):
        """Clear all files from the list."""
        self.file_paths.clear()
        self._update_display()

    def get_selected_files(self) -> List[str]:
        """Get list of selected file paths."""
        return self.file_paths.copy()  # Return all for now

    def get_all_files(self) -> List[str]:
        """Get list of all file paths."""
        return self.file_paths.copy()

    def move_up(self):
        """Move selected items up in the list."""
        # Simplified implementation
        if len(self.file_paths) > 1:
            self.file_paths[0], self.file_paths[1] = self.file_paths[1], self.file_paths[0]
            self._update_display()

    def move_down(self):
        """Move selected items down in the list."""
        # Simplified implementation
        if len(self.file_paths) > 1:
            self.file_paths[-1], self.file_paths[-2] = self.file_paths[-2], self.file_paths[-1]
            self._update_display()

    def _update_display(self):
        """Update the display of files."""
        self.textbox.delete("1.0", "end")

        if not self.file_paths:
            self.textbox.insert("1.0", "No files added yet.\nDrag and drop PDF files or click 'Add Files' to get started.")
        else:
            for i, file_path in enumerate(self.file_paths, 1):
                filename = os.path.basename(file_path)
                self.textbox.insert("end", f"{i}. {filename}\n")
                self.textbox.insert("end", f"   {file_path}\n\n")

    def _on_double_click(self, event):
        """Handle double-click event."""
        if self.file_paths and self.double_click_callback:
            self.double_click_callback(self.file_paths[0])


class ModernSidebar(ctk.CTkFrame):
    """Modern sidebar with navigation and controls."""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)

        # Title
        self.title_label = ctk.CTkLabel(
            self,
            text="PDF Manipulator",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        self.title_label.pack(pady=(20, 30))

        # Theme toggle
        self.theme_label = ctk.CTkLabel(
            self,
            text="Appearance",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.theme_label.pack(pady=(0, 10))

        self.theme_switch = ctk.CTkSwitch(
            self,
            text="Dark Mode",
            command=self._toggle_theme,
            onvalue="dark",
            offvalue="light"
        )
        self.theme_switch.pack(pady=(0, 20))
        self.theme_switch.select()  # Start in dark mode

        # Quick actions
        self.actions_label = ctk.CTkLabel(
            self,
            text="Quick Actions",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.actions_label.pack(pady=(20, 10))

        self.open_button = ctk.CTkButton(
            self,
            text="📂 Open PDF",
            width=180,
            height=35,
            corner_radius=8
        )
        self.open_button.pack(pady=5)

        self.create_button = ctk.CTkButton(
            self,
            text="📝 Create PDF",
            width=180,
            height=35,
            corner_radius=8
        )
        self.create_button.pack(pady=5)

        # Info section
        self.info_frame = ctk.CTkFrame(self, corner_radius=10)
        self.info_frame.pack(fill="x", padx=10, pady=(30, 10))

        self.info_label = ctk.CTkLabel(
            self.info_frame,
            text="ℹ️ Tips",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        self.info_label.pack(pady=(10, 5))

        self.tip_text = ctk.CTkLabel(
            self.info_frame,
            text="• Drag & drop files for quick access\n• Use Ctrl+Z to undo operations\n• Check file info before processing",
            font=ctk.CTkFont(size=11),
            justify="left",
            text_color=("gray60", "gray40")
        )
        self.tip_text.pack(pady=(0, 10), padx=10)

    def _toggle_theme(self):
        """Toggle between dark and light themes."""
        if self.theme_switch.get() == "dark":
            ctk.set_appearance_mode("dark")
        else:
            ctk.set_appearance_mode("light")

    def set_open_command(self, command):
        """Set command for open button."""
        self.open_button.configure(command=command)

    def set_create_command(self, command):
        """Set command for create button."""
        self.create_button.configure(command=command)


class SimpleProgressDialog(tk.Toplevel):
    """Simple, stable progress dialog using standard Tkinter."""

    def __init__(self, parent, title: str = "Processing..."):
        super().__init__(parent)
        self.title(title)
        self.geometry("400x150")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()

        # Center the dialog
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        self.cancelled = False

        # Configure style
        self.configure(bg='#2b2b2b')

        # Main frame
        main_frame = tk.Frame(self, bg='#2b2b2b')
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text=title,
            font=('Arial', 14, 'bold'),
            bg='#2b2b2b',
            fg='white'
        )
        title_label.pack(pady=(0, 15))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame,
            variable=self.progress_var,
            maximum=100,
            length=300
        )
        self.progress_bar.pack(pady=10)

        # Status label
        self.status_var = tk.StringVar(value="Initializing...")
        self.status_label = tk.Label(
            main_frame,
            textvariable=self.status_var,
            font=('Arial', 10),
            bg='#2b2b2b',
            fg='#cccccc'
        )
        self.status_label.pack(pady=5)

        # Cancel button
        self.cancel_button = tk.Button(
            main_frame,
            text="Cancel",
            command=self._on_cancel,
            font=('Arial', 10),
            bg='#555555',
            fg='white',
            relief='flat',
            padx=20,
            pady=5
        )
        self.cancel_button.pack(pady=(10, 0))

        # Bind close event
        self.protocol("WM_DELETE_WINDOW", self._on_cancel)

    def update_progress(self, progress: float, status: str = ""):
        """Update progress bar and status."""
        if self.cancelled:
            return

        try:
            if self.winfo_exists():
                self.progress_var.set(progress)
                if status:
                    self.status_var.set(status)
                self.update_idletasks()
        except Exception:
            pass

    def _on_cancel(self):
        """Handle cancel button click."""
        self.cancelled = True
        try:
            self.destroy()
        except Exception:
            pass

    def safe_destroy(self):
        """Safely destroy the dialog."""
        self.cancelled = True
        try:
            if self.winfo_exists():
                self.destroy()
        except Exception:
            pass
