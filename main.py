"""
PDF Manipulation Desktop Application
A comprehensive GUI application for PDF processing operations.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from tkinterdnd2 import TkinterDnD
import threading
import os
from typing import List, Optional

# Import our custom modules
from pdf_processor import PDFProcessor
from gui_components import (DragDropFrame, ProgressDialog, PageThumbnailFrame,
                           FileListFrame)
from utils import (validate_pdf_file, get_pdf_info, format_file_size,
                  select_output_file, show_error, show_info, center_window,
                  parse_page_range)


class PDFManipulatorApp:
    """Main application class for PDF manipulation."""

    def __init__(self):
        # Initialize main window
        self.root = TkinterDnD.Tk()
        self.root.title("PDF Manipulator - Advanced PDF Processing Tool")
        self.root.geometry("1200x800")
        center_window(self.root, 1200, 800)

        # Initialize PDF processor
        self.pdf_processor = PDFProcessor()

        # Current operation state
        self.current_files = []
        self.current_pdf_path = None
        self.selected_pages = set()

        # Create GUI
        self._create_menu()
        self._create_main_interface()

        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _create_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open PDF...", command=self._open_pdf)
        file_menu.add_command(label="Add PDFs...", command=self._add_pdfs)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Create PDF from Text...", command=self._create_from_text)
        tools_menu.add_command(label="Create PDF from Images...", command=self._create_from_images)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self._show_about)

    def _create_main_interface(self):
        """Create the main interface with tabs."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Create tabs
        self._create_merge_tab()
        self._create_extract_tab()
        self._create_sort_tab()
        self._create_crop_tab()

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = tk.Label(self.root, textvariable=self.status_var,
                                  relief="sunken", anchor="w")
        self.status_bar.pack(side="bottom", fill="x")

    def _create_merge_tab(self):
        """Create the merge PDFs tab."""
        merge_frame = ttk.Frame(self.notebook)
        self.notebook.add(merge_frame, text="Merge PDFs")

        # Left panel - file list
        left_panel = tk.Frame(merge_frame)
        left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        tk.Label(left_panel, text="PDF Files to Merge:", font=("Arial", 12, "bold")).pack(anchor="w")

        # Drag and drop area
        self.merge_drop_frame = DragDropFrame(left_panel, self._on_merge_files_dropped, height=100)
        self.merge_drop_frame.pack(fill="x", pady=5)

        # File list
        self.merge_file_list = FileListFrame(left_panel)
        self.merge_file_list.pack(fill="both", expand=True, pady=5)

        # File list controls
        file_controls = tk.Frame(left_panel)
        file_controls.pack(fill="x", pady=5)

        ttk.Button(file_controls, text="Add Files", command=self._add_merge_files).pack(side="left", padx=2)
        ttk.Button(file_controls, text="Remove Selected", command=self.merge_file_list.remove_selected).pack(side="left", padx=2)
        ttk.Button(file_controls, text="Clear All", command=self.merge_file_list.clear_all).pack(side="left", padx=2)
        ttk.Button(file_controls, text="Move Up", command=self.merge_file_list.move_up).pack(side="left", padx=2)
        ttk.Button(file_controls, text="Move Down", command=self.merge_file_list.move_down).pack(side="left", padx=2)

        # Right panel - options and merge
        right_panel = tk.Frame(merge_frame, width=300)
        right_panel.pack(side="right", fill="y", padx=5, pady=5)
        right_panel.pack_propagate(False)

        tk.Label(right_panel, text="Merge Options:", font=("Arial", 12, "bold")).pack(anchor="w")

        # Output file selection
        output_frame = tk.Frame(right_panel)
        output_frame.pack(fill="x", pady=10)

        tk.Label(output_frame, text="Output File:").pack(anchor="w")
        self.merge_output_var = tk.StringVar()
        output_entry = tk.Entry(output_frame, textvariable=self.merge_output_var, state="readonly")
        output_entry.pack(fill="x", pady=2)

        ttk.Button(output_frame, text="Browse...", command=self._select_merge_output).pack(pady=2)

        # Merge button
        ttk.Button(right_panel, text="Merge PDFs", command=self._merge_pdfs,
                  style="Accent.TButton").pack(pady=20, fill="x")

        # File info display
        info_frame = tk.LabelFrame(right_panel, text="File Information")
        info_frame.pack(fill="both", expand=True, pady=10)

        self.merge_info_text = scrolledtext.ScrolledText(info_frame, height=10, state="disabled")
        self.merge_info_text.pack(fill="both", expand=True, padx=5, pady=5)

        # Set callback for file list selection
        self.merge_file_list.set_selection_callback(self._on_merge_selection_change)

    def _create_extract_tab(self):
        """Create the extract pages tab."""
        extract_frame = ttk.Frame(self.notebook)
        self.notebook.add(extract_frame, text="Extract Pages")

        # Left panel - PDF viewer
        left_panel = tk.Frame(extract_frame)
        left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # PDF file selection
        file_frame = tk.Frame(left_panel)
        file_frame.pack(fill="x", pady=5)

        tk.Label(file_frame, text="PDF File:", font=("Arial", 12, "bold")).pack(anchor="w")

        pdf_select_frame = tk.Frame(file_frame)
        pdf_select_frame.pack(fill="x", pady=2)

        self.extract_pdf_var = tk.StringVar()
        pdf_entry = tk.Entry(pdf_select_frame, textvariable=self.extract_pdf_var, state="readonly")
        pdf_entry.pack(side="left", fill="x", expand=True)

        ttk.Button(pdf_select_frame, text="Browse...", command=self._select_extract_pdf).pack(side="right", padx=(5, 0))

        # Page thumbnails
        tk.Label(left_panel, text="Pages:", font=("Arial", 12, "bold")).pack(anchor="w", pady=(10, 0))

        self.extract_thumbnails = PageThumbnailFrame(left_panel)
        self.extract_thumbnails.pack(fill="both", expand=True, pady=5)
        self.extract_thumbnails.set_selection_callback(self._on_extract_selection_change)

        # Right panel - options and extract
        right_panel = tk.Frame(extract_frame, width=300)
        right_panel.pack(side="right", fill="y", padx=5, pady=5)
        right_panel.pack_propagate(False)

        tk.Label(right_panel, text="Extract Options:", font=("Arial", 12, "bold")).pack(anchor="w")

        # Page selection options
        selection_frame = tk.LabelFrame(right_panel, text="Page Selection")
        selection_frame.pack(fill="x", pady=10)

        ttk.Button(selection_frame, text="Select All", command=self.extract_thumbnails.select_all).pack(fill="x", pady=2)
        ttk.Button(selection_frame, text="Select None", command=self.extract_thumbnails.select_none).pack(fill="x", pady=2)

        # Page range input
        tk.Label(selection_frame, text="Page Range (e.g., 1-3,5,7-9):").pack(anchor="w", pady=(10, 0))
        self.extract_range_var = tk.StringVar()
        range_entry = tk.Entry(selection_frame, textvariable=self.extract_range_var)
        range_entry.pack(fill="x", pady=2)

        ttk.Button(selection_frame, text="Select Range", command=self._select_page_range).pack(fill="x", pady=2)

        # Output file selection
        output_frame = tk.Frame(right_panel)
        output_frame.pack(fill="x", pady=10)

        tk.Label(output_frame, text="Output File:").pack(anchor="w")
        self.extract_output_var = tk.StringVar()
        output_entry = tk.Entry(output_frame, textvariable=self.extract_output_var, state="readonly")
        output_entry.pack(fill="x", pady=2)

        ttk.Button(output_frame, text="Browse...", command=self._select_extract_output).pack(pady=2)

        # Extract button
        ttk.Button(right_panel, text="Extract Pages", command=self._extract_pages,
                  style="Accent.TButton").pack(pady=20, fill="x")

        # Selection info
        self.extract_info_var = tk.StringVar(value="No pages selected")
        tk.Label(right_panel, textvariable=self.extract_info_var, wraplength=280).pack(pady=10)

    def _create_sort_tab(self):
        """Create the sort pages tab."""
        sort_frame = ttk.Frame(self.notebook)
        self.notebook.add(sort_frame, text="Sort Pages")

        # Left panel - PDF viewer
        left_panel = tk.Frame(sort_frame)
        left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # PDF file selection
        file_frame = tk.Frame(left_panel)
        file_frame.pack(fill="x", pady=5)

        tk.Label(file_frame, text="PDF File:", font=("Arial", 12, "bold")).pack(anchor="w")

        pdf_select_frame = tk.Frame(file_frame)
        pdf_select_frame.pack(fill="x", pady=2)

        self.sort_pdf_var = tk.StringVar()
        pdf_entry = tk.Entry(pdf_select_frame, textvariable=self.sort_pdf_var, state="readonly")
        pdf_entry.pack(side="left", fill="x", expand=True)

        ttk.Button(pdf_select_frame, text="Browse...", command=self._select_sort_pdf).pack(side="right", padx=(5, 0))

        # Page thumbnails
        tk.Label(left_panel, text="Pages (drag to reorder):", font=("Arial", 12, "bold")).pack(anchor="w", pady=(10, 0))

        self.sort_thumbnails = PageThumbnailFrame(left_panel)
        self.sort_thumbnails.pack(fill="both", expand=True, pady=5)

        # Right panel - options and sort
        right_panel = tk.Frame(sort_frame, width=300)
        right_panel.pack(side="right", fill="y", padx=5, pady=5)
        right_panel.pack_propagate(False)

        tk.Label(right_panel, text="Sort Options:", font=("Arial", 12, "bold")).pack(anchor="w")

        # Sort options
        sort_options_frame = tk.LabelFrame(right_panel, text="Sort Method")
        sort_options_frame.pack(fill="x", pady=10)

        self.sort_method_var = tk.StringVar(value="manual")

        tk.Radiobutton(sort_options_frame, text="Manual (drag and drop)",
                      variable=self.sort_method_var, value="manual").pack(anchor="w")
        tk.Radiobutton(sort_options_frame, text="Ascending page numbers",
                      variable=self.sort_method_var, value="ascending").pack(anchor="w")
        tk.Radiobutton(sort_options_frame, text="Descending page numbers",
                      variable=self.sort_method_var, value="descending").pack(anchor="w")
        tk.Radiobutton(sort_options_frame, text="Reverse current order",
                      variable=self.sort_method_var, value="reverse").pack(anchor="w")

        ttk.Button(sort_options_frame, text="Apply Sort", command=self._apply_sort).pack(fill="x", pady=5)

        # Output file selection
        output_frame = tk.Frame(right_panel)
        output_frame.pack(fill="x", pady=10)

        tk.Label(output_frame, text="Output File:").pack(anchor="w")
        self.sort_output_var = tk.StringVar()
        output_entry = tk.Entry(output_frame, textvariable=self.sort_output_var, state="readonly")
        output_entry.pack(fill="x", pady=2)

        ttk.Button(output_frame, text="Browse...", command=self._select_sort_output).pack(pady=2)

        # Sort button
        ttk.Button(right_panel, text="Save Sorted PDF", command=self._sort_pages,
                  style="Accent.TButton").pack(pady=20, fill="x")

    def _create_crop_tab(self):
        """Create the crop pages tab."""
        crop_frame = ttk.Frame(self.notebook)
        self.notebook.add(crop_frame, text="Crop Pages")

        # Left panel - PDF viewer
        left_panel = tk.Frame(crop_frame)
        left_panel.pack(side="left", fill="both", expand=True, padx=5, pady=5)

        # PDF file selection
        file_frame = tk.Frame(left_panel)
        file_frame.pack(fill="x", pady=5)

        tk.Label(file_frame, text="PDF File:", font=("Arial", 12, "bold")).pack(anchor="w")

        pdf_select_frame = tk.Frame(file_frame)
        pdf_select_frame.pack(fill="x", pady=2)

        self.crop_pdf_var = tk.StringVar()
        pdf_entry = tk.Entry(pdf_select_frame, textvariable=self.crop_pdf_var, state="readonly")
        pdf_entry.pack(side="left", fill="x", expand=True)

        ttk.Button(pdf_select_frame, text="Browse...", command=self._select_crop_pdf).pack(side="right", padx=(5, 0))

        # Page preview (simplified for this implementation)
        tk.Label(left_panel, text="Page Preview:", font=("Arial", 12, "bold")).pack(anchor="w", pady=(10, 0))

        self.crop_preview_frame = tk.Frame(left_panel, bg="white", relief="sunken", bd=2)
        self.crop_preview_frame.pack(fill="both", expand=True, pady=5)

        # Right panel - crop options
        right_panel = tk.Frame(crop_frame, width=300)
        right_panel.pack(side="right", fill="y", padx=5, pady=5)
        right_panel.pack_propagate(False)

        tk.Label(right_panel, text="Crop Options:", font=("Arial", 12, "bold")).pack(anchor="w")

        # Page selection
        page_frame = tk.LabelFrame(right_panel, text="Page Selection")
        page_frame.pack(fill="x", pady=10)

        tk.Label(page_frame, text="Page Number:").pack(anchor="w")
        self.crop_page_var = tk.StringVar(value="1")
        page_spinbox = tk.Spinbox(page_frame, from_=1, to=1, textvariable=self.crop_page_var,
                                 command=self._update_crop_preview)
        page_spinbox.pack(fill="x", pady=2)
        self.crop_page_spinbox = page_spinbox

        # Crop coordinates
        coords_frame = tk.LabelFrame(right_panel, text="Crop Coordinates (points)")
        coords_frame.pack(fill="x", pady=10)

        tk.Label(coords_frame, text="Left (X0):").pack(anchor="w")
        self.crop_x0_var = tk.StringVar(value="0")
        tk.Entry(coords_frame, textvariable=self.crop_x0_var).pack(fill="x", pady=1)

        tk.Label(coords_frame, text="Top (Y0):").pack(anchor="w")
        self.crop_y0_var = tk.StringVar(value="0")
        tk.Entry(coords_frame, textvariable=self.crop_y0_var).pack(fill="x", pady=1)

        tk.Label(coords_frame, text="Right (X1):").pack(anchor="w")
        self.crop_x1_var = tk.StringVar(value="612")
        tk.Entry(coords_frame, textvariable=self.crop_x1_var).pack(fill="x", pady=1)

        tk.Label(coords_frame, text="Bottom (Y1):").pack(anchor="w")
        self.crop_y1_var = tk.StringVar(value="792")
        tk.Entry(coords_frame, textvariable=self.crop_y1_var).pack(fill="x", pady=1)

        # Preset crop options
        preset_frame = tk.LabelFrame(right_panel, text="Preset Crops")
        preset_frame.pack(fill="x", pady=10)

        ttk.Button(preset_frame, text="Top Half", command=lambda: self._set_crop_preset("top")).pack(fill="x", pady=1)
        ttk.Button(preset_frame, text="Bottom Half", command=lambda: self._set_crop_preset("bottom")).pack(fill="x", pady=1)
        ttk.Button(preset_frame, text="Left Half", command=lambda: self._set_crop_preset("left")).pack(fill="x", pady=1)
        ttk.Button(preset_frame, text="Right Half", command=lambda: self._set_crop_preset("right")).pack(fill="x", pady=1)
        ttk.Button(preset_frame, text="Center Square", command=lambda: self._set_crop_preset("center")).pack(fill="x", pady=1)

        # Apply to pages
        apply_frame = tk.LabelFrame(right_panel, text="Apply To")
        apply_frame.pack(fill="x", pady=10)

        self.crop_apply_var = tk.StringVar(value="current")
        tk.Radiobutton(apply_frame, text="Current page only",
                      variable=self.crop_apply_var, value="current").pack(anchor="w")
        tk.Radiobutton(apply_frame, text="All pages",
                      variable=self.crop_apply_var, value="all").pack(anchor="w")
        tk.Radiobutton(apply_frame, text="Page range:",
                      variable=self.crop_apply_var, value="range").pack(anchor="w")

        self.crop_range_var = tk.StringVar()
        tk.Entry(apply_frame, textvariable=self.crop_range_var).pack(fill="x", pady=2)

        # Output file selection
        output_frame = tk.Frame(right_panel)
        output_frame.pack(fill="x", pady=10)

        tk.Label(output_frame, text="Output File:").pack(anchor="w")
        self.crop_output_var = tk.StringVar()
        output_entry = tk.Entry(output_frame, textvariable=self.crop_output_var, state="readonly")
        output_entry.pack(fill="x", pady=2)

        ttk.Button(output_frame, text="Browse...", command=self._select_crop_output).pack(pady=2)

        # Crop button
        ttk.Button(right_panel, text="Crop Pages", command=self._crop_pages,
                  style="Accent.TButton").pack(pady=20, fill="x")

    # Event handlers for merge tab
    def _on_merge_files_dropped(self, files: List[str]):
        """Handle files dropped on merge tab."""
        valid_files = [f for f in files if validate_pdf_file(f)]
        if valid_files:
            self.merge_file_list.add_files(valid_files)
            self.status_var.set(f"Added {len(valid_files)} PDF files")
        else:
            show_error("Invalid Files", "No valid PDF files were found in the selection.")

    def _add_merge_files(self):
        """Add files to merge list via file dialog."""
        from utils import select_input_files
        files = select_input_files("Select PDF Files to Merge")
        if files:
            self.merge_file_list.add_files(files)
            self.status_var.set(f"Added {len(files)} PDF files")

    def _on_merge_selection_change(self, selected_files: List[str]):
        """Handle selection change in merge file list."""
        self._update_merge_info(selected_files)

    def _update_merge_info(self, files: List[str]):
        """Update file information display for merge tab."""
        self.merge_info_text.config(state="normal")
        self.merge_info_text.delete(1.0, tk.END)

        if not files:
            self.merge_info_text.insert(tk.END, "No files selected")
        else:
            total_pages = 0
            total_size = 0

            for file_path in files:
                info = get_pdf_info(file_path)
                if info:
                    filename = os.path.basename(file_path)
                    self.merge_info_text.insert(tk.END, f"File: {filename}\n")
                    self.merge_info_text.insert(tk.END, f"Pages: {info['page_count']}\n")
                    self.merge_info_text.insert(tk.END, f"Size: {format_file_size(info['file_size'])}\n")
                    if info['title']:
                        self.merge_info_text.insert(tk.END, f"Title: {info['title']}\n")
                    self.merge_info_text.insert(tk.END, "\n")

                    total_pages += info['page_count']
                    total_size += info['file_size']

            if len(files) > 1:
                self.merge_info_text.insert(tk.END, f"Total Pages: {total_pages}\n")
                self.merge_info_text.insert(tk.END, f"Total Size: {format_file_size(total_size)}")

        self.merge_info_text.config(state="disabled")

    def _select_merge_output(self):
        """Select output file for merge operation."""
        output_file = select_output_file("merged_document.pdf", "Save Merged PDF As")
        if output_file:
            self.merge_output_var.set(output_file)

    def _merge_pdfs(self):
        """Perform PDF merge operation."""
        files = self.merge_file_list.get_all_files()
        output_path = self.merge_output_var.get()

        if not files:
            show_error("No Files", "Please add PDF files to merge.")
            return

        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Validate all files
        invalid_files = [f for f in files if not validate_pdf_file(f)]
        if invalid_files:
            show_error("Invalid Files", f"The following files are not valid PDFs:\n" +
                      "\n".join([os.path.basename(f) for f in invalid_files]))
            return

        # Perform merge in separate thread
        def merge_thread():
            progress_dialog = ProgressDialog(self.root, "Merging PDFs...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                success = self.pdf_processor.merge_pdfs(files, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.destroy()

                    if success:
                        show_info("Success", f"PDFs merged successfully!\nOutput: {output_path}")
                        self.status_var.set("Merge completed successfully")
                    else:
                        show_error("Error", "Failed to merge PDFs. Please check the files and try again.")
                        self.status_var.set("Merge failed")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.destroy()
                    show_error("Error", f"An error occurred during merge: {str(e)}")
                    self.status_var.set("Merge failed")

        threading.Thread(target=merge_thread, daemon=True).start()

    # Event handlers for extract tab
    def _select_extract_pdf(self):
        """Select PDF file for extraction."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.extract_pdf_var.set(file_path)
                self._load_extract_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_extract_thumbnails(self, file_path: str):
        """Load thumbnails for extract tab."""
        def load_thread():
            if self.pdf_processor.load_pdf(file_path):
                page_count = self.pdf_processor.get_page_count()

                # Clear existing thumbnails
                self.extract_thumbnails.clear_thumbnails()

                # Load thumbnails
                for page_num in range(page_count):
                    thumbnail = self.pdf_processor.get_page_thumbnail(page_num, 0.3)
                    if thumbnail:
                        self.extract_thumbnails.add_thumbnail(page_num, thumbnail)

                self.status_var.set(f"Loaded {page_count} pages")
            else:
                show_error("Error", "Failed to load PDF file.")

        threading.Thread(target=load_thread, daemon=True).start()

    def _on_extract_selection_change(self, selected_pages: set):
        """Handle page selection change in extract tab."""
        self.selected_pages = selected_pages
        count = len(selected_pages)
        if count == 0:
            self.extract_info_var.set("No pages selected")
        elif count == 1:
            page_num = list(selected_pages)[0] + 1
            self.extract_info_var.set(f"Selected: Page {page_num}")
        else:
            page_list = sorted([p + 1 for p in selected_pages])
            self.extract_info_var.set(f"Selected: {count} pages ({', '.join(map(str, page_list[:5]))}{'...' if count > 5 else ''})")

    def _select_page_range(self):
        """Select pages based on range input."""
        range_text = self.extract_range_var.get().strip()
        if not range_text:
            return

        try:
            max_pages = self.pdf_processor.get_page_count()
            if max_pages == 0:
                show_error("No PDF", "Please select a PDF file first.")
                return

            page_numbers = parse_page_range(range_text, max_pages)

            # Clear current selection
            self.extract_thumbnails.select_none()

            # Select pages in range
            for thumb in self.extract_thumbnails.thumbnails:
                if thumb['page_num'] in page_numbers:
                    thumb['var'].set(True)

            # Update selection
            self.selected_pages = set(page_numbers)
            self._on_extract_selection_change(self.selected_pages)

        except ValueError as e:
            show_error("Invalid Range", f"Invalid page range format: {str(e)}")

    def _select_extract_output(self):
        """Select output file for extract operation."""
        output_file = select_output_file("extracted_pages.pdf", "Save Extracted Pages As")
        if output_file:
            self.extract_output_var.set(output_file)

    def _extract_pages(self):
        """Perform page extraction operation."""
        if not self.selected_pages:
            show_error("No Pages", "Please select pages to extract.")
            return

        output_path = self.extract_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Perform extraction in separate thread
        def extract_thread():
            progress_dialog = ProgressDialog(self.root, "Extracting Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                page_list = sorted(list(self.selected_pages))
                success = self.pdf_processor.extract_pages(page_list, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.destroy()

                    if success:
                        show_info("Success", f"Pages extracted successfully!\nOutput: {output_path}")
                        self.status_var.set("Extraction completed successfully")
                    else:
                        show_error("Error", "Failed to extract pages. Please try again.")
                        self.status_var.set("Extraction failed")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.destroy()
                    show_error("Error", f"An error occurred during extraction: {str(e)}")
                    self.status_var.set("Extraction failed")

        threading.Thread(target=extract_thread, daemon=True).start()

    # Event handlers for sort tab
    def _select_sort_pdf(self):
        """Select PDF file for sorting."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.sort_pdf_var.set(file_path)
                self._load_sort_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_sort_thumbnails(self, file_path: str):
        """Load thumbnails for sort tab."""
        def load_thread():
            if self.pdf_processor.load_pdf(file_path):
                page_count = self.pdf_processor.get_page_count()

                # Clear existing thumbnails
                self.sort_thumbnails.clear_thumbnails()

                # Load thumbnails (non-selectable for sorting)
                for page_num in range(page_count):
                    thumbnail = self.pdf_processor.get_page_thumbnail(page_num, 0.3)
                    if thumbnail:
                        self.sort_thumbnails.add_thumbnail(page_num, thumbnail, selectable=False)

                self.status_var.set(f"Loaded {page_count} pages for sorting")
            else:
                show_error("Error", "Failed to load PDF file.")

        threading.Thread(target=load_thread, daemon=True).start()

    def _apply_sort(self):
        """Apply selected sort method."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        method = self.sort_method_var.get()
        page_count = self.pdf_processor.get_page_count()

        if method == "ascending":
            # Already in ascending order
            show_info("Sort Applied", "Pages are already in ascending order.")
        elif method == "descending":
            # Reverse order
            show_info("Sort Applied", "Pages will be sorted in descending order when saved.")
        elif method == "reverse":
            # Reverse current order
            show_info("Sort Applied", "Pages will be reversed when saved.")
        else:
            show_info("Manual Sort", "Use drag and drop to manually reorder pages (feature to be implemented).")

    def _select_sort_output(self):
        """Select output file for sort operation."""
        output_file = select_output_file("sorted_document.pdf", "Save Sorted PDF As")
        if output_file:
            self.sort_output_var.set(output_file)

    def _sort_pages(self):
        """Perform page sorting operation."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        output_path = self.sort_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        method = self.sort_method_var.get()
        page_count = self.pdf_processor.get_page_count()

        # Determine page order based on sort method
        if method == "ascending":
            page_order = list(range(page_count))
        elif method == "descending":
            page_order = list(range(page_count - 1, -1, -1))
        elif method == "reverse":
            page_order = list(range(page_count - 1, -1, -1))
        else:  # manual
            # For now, use original order (manual drag-drop would be implemented here)
            page_order = list(range(page_count))

        # Perform sorting in separate thread
        def sort_thread():
            progress_dialog = ProgressDialog(self.root, "Sorting Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                success = self.pdf_processor.sort_pages(page_order, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.destroy()

                    if success:
                        show_info("Success", f"Pages sorted successfully!\nOutput: {output_path}")
                        self.status_var.set("Sort completed successfully")
                    else:
                        show_error("Error", "Failed to sort pages. Please try again.")
                        self.status_var.set("Sort failed")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.destroy()
                    show_error("Error", f"An error occurred during sorting: {str(e)}")
                    self.status_var.set("Sort failed")

        threading.Thread(target=sort_thread, daemon=True).start()

    # Event handlers for crop tab
    def _select_crop_pdf(self):
        """Select PDF file for cropping."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.crop_pdf_var.set(file_path)
                self._load_crop_pdf(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_crop_pdf(self, file_path: str):
        """Load PDF for cropping."""
        if self.pdf_processor.load_pdf(file_path):
            page_count = self.pdf_processor.get_page_count()

            # Update page spinbox
            self.crop_page_spinbox.config(to=page_count)

            # Load first page preview
            self._update_crop_preview()

            self.status_var.set(f"Loaded PDF with {page_count} pages")
        else:
            show_error("Error", "Failed to load PDF file.")

    def _update_crop_preview(self):
        """Update crop preview (simplified implementation)."""
        # This would show a preview of the selected page with crop rectangle
        # For now, just update the status
        try:
            page_num = int(self.crop_page_var.get()) - 1
            if self.pdf_processor.current_doc and 0 <= page_num < self.pdf_processor.get_page_count():
                self.status_var.set(f"Viewing page {page_num + 1}")
        except ValueError:
            pass

    def _set_crop_preset(self, preset: str):
        """Set crop coordinates based on preset."""
        # Default page size (US Letter: 612 x 792 points)
        width, height = 612, 792

        if preset == "top":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height // 2))
        elif preset == "bottom":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set(str(height // 2))
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height))
        elif preset == "left":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width // 2))
            self.crop_y1_var.set(str(height))
        elif preset == "right":
            self.crop_x0_var.set(str(width // 2))
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height))
        elif preset == "center":
            margin = min(width, height) // 4
            self.crop_x0_var.set(str(margin))
            self.crop_y0_var.set(str(margin))
            self.crop_x1_var.set(str(width - margin))
            self.crop_y1_var.set(str(height - margin))

    def _select_crop_output(self):
        """Select output file for crop operation."""
        output_file = select_output_file("cropped_document.pdf", "Save Cropped PDF As")
        if output_file:
            self.crop_output_var.set(output_file)

    def _crop_pages(self):
        """Perform page cropping operation."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        output_path = self.crop_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Get crop coordinates
        try:
            x0 = float(self.crop_x0_var.get())
            y0 = float(self.crop_y0_var.get())
            x1 = float(self.crop_x1_var.get())
            y1 = float(self.crop_y1_var.get())

            if x0 >= x1 or y0 >= y1:
                show_error("Invalid Coordinates", "Invalid crop coordinates. Please check the values.")
                return

            crop_rect = (x0, y0, x1, y1)
        except ValueError:
            show_error("Invalid Coordinates", "Please enter valid numeric coordinates.")
            return

        # Determine which pages to crop
        apply_to = self.crop_apply_var.get()
        page_count = self.pdf_processor.get_page_count()

        if apply_to == "current":
            try:
                page_num = int(self.crop_page_var.get()) - 1
                if 0 <= page_num < page_count:
                    page_numbers = [page_num]
                else:
                    show_error("Invalid Page", "Invalid page number.")
                    return
            except ValueError:
                show_error("Invalid Page", "Please enter a valid page number.")
                return
        elif apply_to == "all":
            page_numbers = list(range(page_count))
        else:  # range
            try:
                range_text = self.crop_range_var.get().strip()
                if not range_text:
                    show_error("No Range", "Please enter a page range.")
                    return
                page_numbers = parse_page_range(range_text, page_count)
            except ValueError as e:
                show_error("Invalid Range", f"Invalid page range: {str(e)}")
                return

        # Perform cropping in separate thread
        def crop_thread():
            progress_dialog = ProgressDialog(self.root, "Cropping Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                success = self.pdf_processor.crop_pages(page_numbers, crop_rect, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.destroy()

                    if success:
                        show_info("Success", f"Pages cropped successfully!\nOutput: {output_path}")
                        self.status_var.set("Crop completed successfully")
                    else:
                        show_error("Error", "Failed to crop pages. Please try again.")
                        self.status_var.set("Crop failed")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.destroy()
                    show_error("Error", f"An error occurred during cropping: {str(e)}")
                    self.status_var.set("Crop failed")

        threading.Thread(target=crop_thread, daemon=True).start()

    # Menu event handlers
    def _open_pdf(self):
        """Open a PDF file."""
        from utils import select_input_files
        files = select_input_files("Open PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                # Switch to extract tab and load the file
                self.notebook.select(1)  # Extract tab
                self.extract_pdf_var.set(file_path)
                self._load_extract_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _add_pdfs(self):
        """Add PDF files to merge list."""
        from utils import select_input_files
        files = select_input_files("Add PDF Files")
        if files:
            # Switch to merge tab and add files
            self.notebook.select(0)  # Merge tab
            self.merge_file_list.add_files(files)
            self.status_var.set(f"Added {len(files)} PDF files")

    def _create_from_text(self):
        """Create PDF from text input."""
        # Create a dialog for text input
        dialog = tk.Toplevel(self.root)
        dialog.title("Create PDF from Text")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # Text input area
        tk.Label(dialog, text="Enter text content:", font=("Arial", 12, "bold")).pack(anchor="w", padx=10, pady=5)

        text_area = scrolledtext.ScrolledText(dialog, wrap=tk.WORD, width=70, height=20)
        text_area.pack(fill="both", expand=True, padx=10, pady=5)

        # Font options
        options_frame = tk.Frame(dialog)
        options_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(options_frame, text="Font Size:").pack(side="left")
        font_size_var = tk.StringVar(value="12")
        font_size_spinbox = tk.Spinbox(options_frame, from_=8, to=72, textvariable=font_size_var, width=5)
        font_size_spinbox.pack(side="left", padx=5)

        # Buttons
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=10)

        def create_pdf():
            text_content = text_area.get(1.0, tk.END).strip()
            if not text_content:
                show_error("No Text", "Please enter some text content.")
                return

            output_file = select_output_file("text_document.pdf", "Save PDF As")
            if output_file:
                try:
                    font_size = int(font_size_var.get())
                    success = self.pdf_processor.create_pdf_from_text(text_content, output_file, font_size)

                    if success:
                        show_info("Success", f"PDF created successfully!\nOutput: {output_file}")
                        dialog.destroy()
                    else:
                        show_error("Error", "Failed to create PDF from text.")
                except ValueError:
                    show_error("Invalid Font Size", "Please enter a valid font size.")

        ttk.Button(button_frame, text="Create PDF", command=create_pdf).pack(side="right", padx=5)
        ttk.Button(button_frame, text="Cancel", command=dialog.destroy).pack(side="right")

    def _create_from_images(self):
        """Create PDF from image files."""
        # Select image files
        image_files = filedialog.askopenfilenames(
            title="Select Image Files",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if not image_files:
            return

        output_file = select_output_file("image_document.pdf", "Save PDF As")
        if output_file:
            # Create PDF in separate thread
            def create_thread():
                progress_dialog = ProgressDialog(self.root, "Creating PDF from Images...")

                def progress_callback(progress, status):
                    if not progress_dialog.cancelled:
                        progress_dialog.update_progress(progress, status)

                try:
                    success = self.pdf_processor.create_pdf_from_images(list(image_files), output_file, progress_callback)

                    if not progress_dialog.cancelled:
                        progress_dialog.destroy()

                        if success:
                            show_info("Success", f"PDF created successfully!\nOutput: {output_file}")
                            self.status_var.set("PDF creation completed successfully")
                        else:
                            show_error("Error", "Failed to create PDF from images.")
                            self.status_var.set("PDF creation failed")
                except Exception as e:
                    if not progress_dialog.cancelled:
                        progress_dialog.destroy()
                        show_error("Error", f"An error occurred: {str(e)}")
                        self.status_var.set("PDF creation failed")

            threading.Thread(target=create_thread, daemon=True).start()

    def _show_about(self):
        """Show about dialog."""
        about_text = """PDF Manipulator v1.0

A comprehensive PDF processing application with the following features:

• Merge multiple PDF files
• Extract specific pages from PDFs
• Sort PDF pages manually or automatically
• Crop pages with custom coordinates
• Create PDFs from text or images
• Drag and drop support
• Progress tracking for all operations

Built with Python, Tkinter, and PyMuPDF (fitz)

© 2024 PDF Manipulator"""

        messagebox.showinfo("About PDF Manipulator", about_text)

    def _on_closing(self):
        """Handle application closing."""
        # Close any open PDF documents
        self.pdf_processor.close_current_pdf()

        # Destroy the main window
        self.root.destroy()

    def run(self):
        """Start the application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = PDFManipulatorApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application: {e}")


if __name__ == "__main__":
    main()
