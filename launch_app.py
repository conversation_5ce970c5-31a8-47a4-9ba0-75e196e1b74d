"""
Simple launcher for PDF Manipulator Pro with options.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import sys
import os
from pathlib import Path


class AppLauncher:
    """Simple launcher to choose between different versions."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PDF Manipulator Pro - Launcher")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"500x400+{x}+{y}")
        
        # Set icon if available
        try:
            if os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
        except Exception:
            pass
        
        self.create_interface()
    
    def create_interface(self):
        """Create the launcher interface."""
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="PDF Manipulator Pro",
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#1f538d'
        )
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(
            main_frame,
            text="Choose your preferred interface",
            font=('Arial', 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Version options
        options_frame = tk.Frame(main_frame, bg='#f0f0f0')
        options_frame.pack(fill="x", pady=20)
        
        # Modern UI option
        modern_frame = tk.LabelFrame(
            options_frame,
            text="Modern UI (Recommended)",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#1f538d',
            padx=15,
            pady=15
        )
        modern_frame.pack(fill="x", pady=(0, 15))
        
        modern_desc = tk.Label(
            modern_frame,
            text="• Contemporary CustomTkinter interface\n• Dark/Light theme support\n• Welcome screen with logo\n• Modern styling and animations\n• Professional appearance",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#333333',
            justify='left'
        )
        modern_desc.pack(anchor='w', pady=(0, 10))
        
        modern_button = tk.Button(
            modern_frame,
            text="🚀 Launch Modern UI",
            command=self.launch_modern,
            font=('Arial', 12, 'bold'),
            bg='#1f538d',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            cursor='hand2'
        )
        modern_button.pack()
        
        # Classic UI option
        classic_frame = tk.LabelFrame(
            options_frame,
            text="Classic UI",
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#666666',
            padx=15,
            pady=15
        )
        classic_frame.pack(fill="x", pady=(15, 0))
        
        classic_desc = tk.Label(
            classic_frame,
            text="• Traditional Tkinter interface\n• System default styling\n• Menu-based navigation\n• Compatible with all systems\n• Lightweight and fast",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#333333',
            justify='left'
        )
        classic_desc.pack(anchor='w', pady=(0, 10))
        
        classic_button = tk.Button(
            classic_frame,
            text="📋 Launch Classic UI",
            command=self.launch_classic,
            font=('Arial', 12, 'bold'),
            bg='#4CAF50',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            cursor='hand2'
        )
        classic_button.pack()
        
        # Additional options
        extras_frame = tk.Frame(main_frame, bg='#f0f0f0')
        extras_frame.pack(fill="x", pady=(30, 0))
        
        demo_button = tk.Button(
            extras_frame,
            text="📖 Compare Versions",
            command=self.launch_demo,
            font=('Arial', 10),
            bg='#ff6b35',
            fg='white',
            relief='flat',
            padx=15,
            pady=5,
            cursor='hand2'
        )
        demo_button.pack(side="left", padx=(0, 10))
        
        about_button = tk.Button(
            extras_frame,
            text="ℹ️ About",
            command=self.show_about,
            font=('Arial', 10),
            bg='#666666',
            fg='white',
            relief='flat',
            padx=15,
            pady=5,
            cursor='hand2'
        )
        about_button.pack(side="left")
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Choose an interface to launch")
        status_label = tk.Label(
            main_frame,
            textvariable=self.status_var,
            font=('Arial', 9),
            bg='#f0f0f0',
            fg='#888888'
        )
        status_label.pack(side="bottom", pady=(20, 0))
    
    def launch_modern(self):
        """Launch the modern UI version."""
        self.status_var.set("Launching Modern UI...")
        self.root.update()
        
        try:
            if Path("modern_main.py").exists():
                subprocess.Popen([sys.executable, "modern_main.py"])
                self.status_var.set("Modern UI launched successfully!")
                self.root.after(2000, self.root.destroy)  # Close launcher after 2 seconds
            else:
                self.status_var.set("Error: modern_main.py not found")
                messagebox.showerror("Error", "Modern UI file not found.\nPlease ensure modern_main.py exists.")
        except Exception as e:
            self.status_var.set(f"Error launching Modern UI: {str(e)}")
            messagebox.showerror("Error", f"Failed to launch Modern UI:\n{str(e)}")
    
    def launch_classic(self):
        """Launch the classic UI version."""
        self.status_var.set("Launching Classic UI...")
        self.root.update()
        
        try:
            if Path("main.py").exists():
                subprocess.Popen([sys.executable, "main.py"])
                self.status_var.set("Classic UI launched successfully!")
                self.root.after(2000, self.root.destroy)  # Close launcher after 2 seconds
            else:
                self.status_var.set("Error: main.py not found")
                messagebox.showerror("Error", "Classic UI file not found.\nPlease ensure main.py exists.")
        except Exception as e:
            self.status_var.set(f"Error launching Classic UI: {str(e)}")
            messagebox.showerror("Error", f"Failed to launch Classic UI:\n{str(e)}")
    
    def launch_demo(self):
        """Launch the demo comparison."""
        self.status_var.set("Launching comparison demo...")
        self.root.update()
        
        try:
            if Path("demo_comparison.py").exists():
                subprocess.Popen([sys.executable, "demo_comparison.py"])
                self.status_var.set("Demo launched successfully!")
            else:
                self.status_var.set("Error: demo_comparison.py not found")
                messagebox.showerror("Error", "Demo file not found.\nPlease ensure demo_comparison.py exists.")
        except Exception as e:
            self.status_var.set(f"Error launching demo: {str(e)}")
            messagebox.showerror("Error", f"Failed to launch demo:\n{str(e)}")
    
    def show_about(self):
        """Show about information."""
        about_text = """PDF Manipulator Pro v2.0

A comprehensive PDF processing application with both Classic and Modern interfaces.

Features:
• Merge multiple PDF files
• Extract specific pages
• Sort pages automatically or manually
• Crop pages with custom coordinates
• Create PDFs from text or images
• Modern UI with dark/light themes
• Professional welcome screen

Built with Python, Tkinter, CustomTkinter, and PyMuPDF.

© 2024 PDF Manipulator Pro"""
        
        messagebox.showinfo("About PDF Manipulator Pro", about_text)
    
    def run(self):
        """Start the launcher."""
        self.root.mainloop()


def main():
    """Main entry point."""
    print("PDF Manipulator Pro Launcher")
    print("============================")
    
    # Check available versions
    modern_available = Path("modern_main.py").exists()
    classic_available = Path("main.py").exists()
    
    if not modern_available and not classic_available:
        print("Error: No application files found.")
        print("Please ensure either main.py or modern_main.py exists.")
        return
    
    print(f"Modern UI: {'Available' if modern_available else 'Not found'}")
    print(f"Classic UI: {'Available' if classic_available else 'Not found'}")
    print("\nStarting launcher...")
    
    try:
        launcher = AppLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")


if __name__ == "__main__":
    main()
