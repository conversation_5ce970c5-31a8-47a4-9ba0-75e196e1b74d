# PDF Manipulator - Installation Guide

## Quick Start

### Option 1: Run from Source (Recommended for Development)

1. **Download/Clone the project files**
2. **Open Command Prompt or PowerShell in the project folder**
3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
4. **Run the application:**
   ```bash
   python main.py
   ```

### Option 2: Build Executable (For Distribution)

1. **Follow Option 1 first to install dependencies**
2. **Run the build script:**
   ```bash
   python build_exe.py
   ```
3. **Find the executable in the `dist` folder**
4. **Distribute `PDFManipulator.exe` to other computers**

### Option 3: Automated Installation (Windows)

1. **Double-click `install.bat`**
2. **Follow the prompts**
3. **The script will install dependencies and build the executable**

## System Requirements

- **Operating System:** Windows 10/11 (primary), macOS, Linux
- **Python:** 3.7 or higher
- **Memory:** 4GB RAM minimum, 8GB recommended for large PDFs
- **Storage:** 100MB for application, additional space for PDF processing

## Dependencies

The application requires these Python packages:

- **PyMuPDF (fitz) 1.23.14** - PDF processing engine
- **Pillow 10.1.0** - Image processing
- **tkinterdnd2 0.3.0** - Drag and drop support
- **PyInstaller 6.3.0** - Executable building (optional)

## Troubleshooting

### Common Installation Issues

1. **"pip is not recognized"**
   - Install Python from python.org
   - Make sure "Add Python to PATH" is checked during installation

2. **Permission errors during installation**
   - Run Command Prompt as Administrator
   - Or use: `pip install --user -r requirements.txt`

3. **PyMuPDF installation fails**
   - Update pip: `pip install --upgrade pip`
   - Try: `pip install --upgrade PyMuPDF`

4. **tkinterdnd2 not working**
   - Reinstall: `pip uninstall tkinterdnd2 && pip install tkinterdnd2==0.3.0`

### Runtime Issues

1. **Application won't start**
   - Check Python version: `python --version`
   - Verify all dependencies: `pip list`
   - Run: `python test_functionality.py` to check core functions

2. **Drag and drop not working**
   - Ensure tkinterdnd2 is installed correctly
   - Try using the "Browse" buttons instead

3. **Large PDF files cause crashes**
   - Close other applications to free memory
   - Process files in smaller batches
   - Use page extraction for very large documents

4. **Executable doesn't run on other computers**
   - Ensure target computer has Windows 10/11
   - Try running as Administrator
   - Check Windows Defender/antivirus settings

## Building for Distribution

### Creating a Standalone Executable

The `build_exe.py` script creates a single executable file that includes all dependencies:

```bash
python build_exe.py
```

**Build Features:**
- Single file executable (no installation required)
- No console window (clean GUI)
- Includes all Python dependencies
- Optimized file size (~50-100MB)

### Customizing the Build

Edit `build_exe.py` to customize:
- Application icon (add `icon.ico` file)
- Additional data files
- Hidden imports
- Build options

### Distribution Checklist

- [ ] Test executable on clean Windows machine
- [ ] Include README.md with usage instructions
- [ ] Test with various PDF files
- [ ] Verify all features work in executable
- [ ] Check file size and startup time

## Performance Optimization

### For Large PDF Files

1. **Memory Management:**
   - Close PDFs when done processing
   - Restart application for large batches
   - Use page extraction for huge documents

2. **Processing Speed:**
   - Use SSD storage for temporary files
   - Close other applications during processing
   - Process files locally (not over network)

3. **File Size Limits:**
   - Individual PDFs: Up to 500MB recommended
   - Total batch size: Up to 1GB recommended
   - Page count: Up to 1000 pages per operation

### System Optimization

- **RAM:** 8GB+ for large PDF processing
- **Storage:** SSD recommended for temp files
- **CPU:** Multi-core processor for faster processing

## Security Considerations

### File Handling

- Application only reads/writes PDF files you select
- No network connections or data transmission
- Temporary files are cleaned up automatically
- No personal data collection

### Antivirus Software

Some antivirus programs may flag the executable:
- This is common with PyInstaller executables
- Add exception for PDFManipulator.exe
- Or run from source code instead

## Getting Help

### Self-Help Resources

1. **Check this guide** for common solutions
2. **Run test script:** `python test_functionality.py`
3. **Check file permissions** and available disk space
4. **Try with smaller PDF files** first

### Reporting Issues

When reporting problems, include:
- Operating system and version
- Python version (`python --version`)
- Error messages (full text)
- Steps to reproduce the issue
- Sample PDF files (if possible)

### Feature Requests

The application is designed to be modular and extensible. Common requests:
- Additional crop presets
- Batch processing improvements
- More image formats
- Password-protected PDF support

## Advanced Usage

### Command Line Options

Currently, the application is GUI-only, but the core modules can be used programmatically:

```python
from pdf_processor import PDFProcessor

processor = PDFProcessor()
processor.load_pdf("input.pdf")
processor.extract_pages([0, 1, 2], "output.pdf")
```

### Customization

- Modify `gui_components.py` for UI changes
- Edit `pdf_processor.py` for new PDF operations
- Update `utils.py` for additional utilities

---

**PDF Manipulator** - Professional PDF processing made simple.
