"""
Modern PDF Manipulation Desktop Application
A contemporary GUI application for PDF processing operations using CustomTkinter.
"""

import customtkinter as ctk
from tkinter import messagebox, filedialog
from tkinterdnd2 import TkinterDnD
import threading
import os
from typing import List, Optional

# Import our custom modules
from pdf_processor import PDFProcessor
from modern_gui_components import (
    ModernDragDropFrame, SimpleProgressDialog, ModernPageThumbnailFrame,
    ModernFileListFrame, ModernSidebar
)
from utils import (
    validate_pdf_file, get_pdf_info, format_file_size,
    select_output_file, show_error, show_info, center_window,
    parse_page_range
)


class ModernPDFManipulatorApp:
    """Modern main application class for PDF manipulation."""

    def __init__(self):
        # Initialize main window with CustomTkinter
        self.root = TkinterDnD.Tk()
        self.root.title("PDF Manipulator Pro - Modern PDF Processing")
        self.root.geometry("1400x900")

        # Configure window
        self.root.configure(bg='#1a1a1a')
        center_window(self.root, 1400, 900)

        # Set CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Initialize PDF processor
        self.pdf_processor = PDFProcessor()

        # Current operation state
        self.current_files = []
        self.current_pdf_path = None
        self.selected_pages = set()

        # Create modern GUI
        self._create_main_interface()

        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

    def _create_main_interface(self):
        """Create the modern interface with sidebar and main content."""
        # Main container
        self.main_container = ctk.CTkFrame(self.root, corner_radius=0)
        self.main_container.pack(fill="both", expand=True)

        # Configure grid
        self.main_container.grid_columnconfigure(1, weight=1)
        self.main_container.grid_rowconfigure(0, weight=1)

        # Sidebar
        self.sidebar = ModernSidebar(self.main_container, width=250, corner_radius=0)
        self.sidebar.grid(row=0, column=0, sticky="nsew")
        self.sidebar.set_open_command(self._open_pdf)
        self.sidebar.set_create_command(self._show_create_menu)

        # Main content area
        self.content_frame = ctk.CTkFrame(self.main_container, corner_radius=0)
        self.content_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 0))

        # Create tabview for different operations
        self.tabview = ctk.CTkTabview(self.content_frame, corner_radius=15)
        self.tabview.pack(fill="both", expand=True, padx=20, pady=20)

        # Create tabs
        self._create_merge_tab()
        self._create_extract_tab()
        self._create_sort_tab()
        self._create_crop_tab()

        # Status bar
        self.status_frame = ctk.CTkFrame(self.content_frame, height=40, corner_radius=10)
        self.status_frame.pack(fill="x", padx=20, pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🟢 Ready - Select an operation to get started",
            font=ctk.CTkFont(size=12),
            anchor="w"
        )
        self.status_label.pack(side="left", padx=15, pady=10)

    def _create_merge_tab(self):
        """Create the modern merge PDFs tab."""
        merge_tab = self.tabview.add("🔄 Merge PDFs")

        # Configure grid
        merge_tab.grid_columnconfigure(0, weight=2)
        merge_tab.grid_columnconfigure(1, weight=1)
        merge_tab.grid_rowconfigure(0, weight=1)

        # Left panel - file management
        left_panel = ctk.CTkFrame(merge_tab, corner_radius=15)
        left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=10)

        # Title
        title_label = ctk.CTkLabel(
            left_panel,
            text="📁 PDF Files to Merge",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # Drag and drop area
        self.merge_drop_frame = ModernDragDropFrame(
            left_panel,
            self._on_merge_files_dropped,
            height=120
        )
        self.merge_drop_frame.pack(fill="x", padx=20, pady=10)

        # File list
        self.merge_file_list = ModernFileListFrame(left_panel)
        self.merge_file_list.pack(fill="both", expand=True, padx=20, pady=10)

        # File controls
        controls_frame = ctk.CTkFrame(left_panel, corner_radius=10)
        controls_frame.pack(fill="x", padx=20, pady=(0, 20))

        # Control buttons in a grid
        controls_frame.grid_columnconfigure((0, 1, 2), weight=1)

        add_btn = ctk.CTkButton(
            controls_frame,
            text="➕ Add Files",
            command=self._add_merge_files,
            height=35,
            corner_radius=8
        )
        add_btn.grid(row=0, column=0, padx=5, pady=10, sticky="ew")

        remove_btn = ctk.CTkButton(
            controls_frame,
            text="➖ Remove",
            command=self.merge_file_list.remove_selected,
            height=35,
            corner_radius=8,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        remove_btn.grid(row=0, column=1, padx=5, pady=10, sticky="ew")

        clear_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ Clear All",
            command=self.merge_file_list.clear_all,
            height=35,
            corner_radius=8,
            fg_color=("red", "darkred"),
            hover_color=("darkred", "red")
        )
        clear_btn.grid(row=0, column=2, padx=5, pady=10, sticky="ew")

        # Right panel - options and merge
        right_panel = ctk.CTkFrame(merge_tab, corner_radius=15)
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=10)

        # Title
        options_title = ctk.CTkLabel(
            right_panel,
            text="⚙️ Merge Options",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        options_title.pack(pady=(20, 20))

        # Output file selection
        output_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        output_frame.pack(fill="x", padx=20, pady=10)

        output_label = ctk.CTkLabel(
            output_frame,
            text="📄 Output File:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        output_label.pack(pady=(15, 5))

        self.merge_output_var = ctk.StringVar()
        self.merge_output_entry = ctk.CTkEntry(
            output_frame,
            textvariable=self.merge_output_var,
            placeholder_text="Select output location...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.merge_output_entry.pack(fill="x", padx=15, pady=5)

        browse_btn = ctk.CTkButton(
            output_frame,
            text="📂 Browse",
            command=self._select_merge_output,
            height=35,
            width=120,
            corner_radius=8
        )
        browse_btn.pack(pady=(5, 15))

        # Merge button
        self.merge_button = ctk.CTkButton(
            right_panel,
            text="🔄 Merge PDFs",
            command=self._merge_pdfs,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=12
        )
        self.merge_button.pack(fill="x", padx=20, pady=20)

        # File info display
        info_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        info_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        info_title = ctk.CTkLabel(
            info_frame,
            text="📊 File Information",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        info_title.pack(pady=(15, 10))

        self.merge_info_text = ctk.CTkTextbox(
            info_frame,
            corner_radius=8,
            font=ctk.CTkFont(size=11)
        )
        self.merge_info_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # Set callback for file list changes
        self.merge_file_list.set_selection_callback(self._on_merge_selection_change)

    def _create_extract_tab(self):
        """Create the modern extract pages tab."""
        extract_tab = self.tabview.add("📄 Extract Pages")

        # Configure grid
        extract_tab.grid_columnconfigure(0, weight=2)
        extract_tab.grid_columnconfigure(1, weight=1)
        extract_tab.grid_rowconfigure(0, weight=1)

        # Left panel - PDF viewer
        left_panel = ctk.CTkFrame(extract_tab, corner_radius=15)
        left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=10)

        # PDF file selection
        file_frame = ctk.CTkFrame(left_panel, corner_radius=10)
        file_frame.pack(fill="x", padx=20, pady=20)

        file_title = ctk.CTkLabel(
            file_frame,
            text="📂 Select PDF File",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title.pack(pady=(15, 10))

        pdf_select_frame = ctk.CTkFrame(file_frame, corner_radius=8)
        pdf_select_frame.pack(fill="x", padx=15, pady=(0, 15))

        self.extract_pdf_var = ctk.StringVar()
        self.extract_pdf_entry = ctk.CTkEntry(
            pdf_select_frame,
            textvariable=self.extract_pdf_var,
            placeholder_text="No PDF file selected...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.extract_pdf_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)

        browse_pdf_btn = ctk.CTkButton(
            pdf_select_frame,
            text="📂",
            command=self._select_extract_pdf,
            width=40,
            height=35,
            corner_radius=8
        )
        browse_pdf_btn.pack(side="right", padx=(5, 10), pady=10)

        # Page thumbnails
        pages_title = ctk.CTkLabel(
            left_panel,
            text="📑 Pages Preview",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        pages_title.pack(pady=(10, 10))

        self.extract_thumbnails = ModernPageThumbnailFrame(left_panel, corner_radius=10)
        self.extract_thumbnails.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        self.extract_thumbnails.set_selection_callback(self._on_extract_selection_change)

        # Right panel - options and extract
        right_panel = ctk.CTkFrame(extract_tab, corner_radius=15)
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=10)

        # Title
        extract_title = ctk.CTkLabel(
            right_panel,
            text="✂️ Extract Options",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        extract_title.pack(pady=(20, 20))

        # Page selection options
        selection_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        selection_frame.pack(fill="x", padx=20, pady=10)

        selection_title = ctk.CTkLabel(
            selection_frame,
            text="🎯 Page Selection",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        selection_title.pack(pady=(15, 10))

        select_all_btn = ctk.CTkButton(
            selection_frame,
            text="✅ Select All",
            command=self.extract_thumbnails.select_all,
            height=32,
            corner_radius=8
        )
        select_all_btn.pack(fill="x", padx=15, pady=2)

        select_none_btn = ctk.CTkButton(
            selection_frame,
            text="❌ Select None",
            command=self.extract_thumbnails.select_none,
            height=32,
            corner_radius=8,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        select_none_btn.pack(fill="x", padx=15, pady=(2, 15))

        # Output and extract
        output_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        output_frame.pack(fill="x", padx=20, pady=10)

        output_title = ctk.CTkLabel(
            output_frame,
            text="💾 Output File",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        output_title.pack(pady=(15, 10))

        self.extract_output_var = ctk.StringVar()
        self.extract_output_entry = ctk.CTkEntry(
            output_frame,
            textvariable=self.extract_output_var,
            placeholder_text="Select output location...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.extract_output_entry.pack(fill="x", padx=15, pady=5)

        browse_output_btn = ctk.CTkButton(
            output_frame,
            text="📂 Browse",
            command=self._select_extract_output,
            height=35,
            width=120,
            corner_radius=8
        )
        browse_output_btn.pack(pady=(5, 15))

        # Extract button
        self.extract_button = ctk.CTkButton(
            right_panel,
            text="✂️ Extract Pages",
            command=self._extract_pages,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=12
        )
        self.extract_button.pack(fill="x", padx=20, pady=20)

        # Selection info
        self.extract_info_var = ctk.StringVar(value="No pages selected")
        self.extract_info_label = ctk.CTkLabel(
            right_panel,
            textvariable=self.extract_info_var,
            font=ctk.CTkFont(size=12),
            text_color=("gray60", "gray40"),
            wraplength=250
        )
        self.extract_info_label.pack(pady=10)

    def _create_sort_tab(self):
        """Create the modern sort pages tab."""
        sort_tab = self.tabview.add("🔀 Sort Pages")

        # Configure grid
        sort_tab.grid_columnconfigure(0, weight=2)
        sort_tab.grid_columnconfigure(1, weight=1)
        sort_tab.grid_rowconfigure(0, weight=1)

        # Left panel - PDF viewer
        left_panel = ctk.CTkFrame(sort_tab, corner_radius=15)
        left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=10)

        # PDF file selection
        file_frame = ctk.CTkFrame(left_panel, corner_radius=10)
        file_frame.pack(fill="x", padx=20, pady=20)

        file_title = ctk.CTkLabel(
            file_frame,
            text="📂 Select PDF File",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title.pack(pady=(15, 10))

        pdf_select_frame = ctk.CTkFrame(file_frame, corner_radius=8)
        pdf_select_frame.pack(fill="x", padx=15, pady=(0, 15))

        self.sort_pdf_var = ctk.StringVar()
        self.sort_pdf_entry = ctk.CTkEntry(
            pdf_select_frame,
            textvariable=self.sort_pdf_var,
            placeholder_text="No PDF file selected...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.sort_pdf_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)

        browse_sort_btn = ctk.CTkButton(
            pdf_select_frame,
            text="📂",
            command=self._select_sort_pdf,
            width=40,
            height=35,
            corner_radius=8
        )
        browse_sort_btn.pack(side="right", padx=(5, 10), pady=10)

        # Page thumbnails
        pages_title = ctk.CTkLabel(
            left_panel,
            text="📑 Pages (Drag to Reorder)",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        pages_title.pack(pady=(10, 10))

        self.sort_thumbnails = ModernPageThumbnailFrame(left_panel, corner_radius=10)
        self.sort_thumbnails.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Right panel - sort options
        right_panel = ctk.CTkFrame(sort_tab, corner_radius=15)
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=10)

        # Title
        sort_title = ctk.CTkLabel(
            right_panel,
            text="🔀 Sort Options",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        sort_title.pack(pady=(20, 20))

        # Sort method selection
        method_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        method_frame.pack(fill="x", padx=20, pady=10)

        method_title = ctk.CTkLabel(
            method_frame,
            text="📋 Sort Method",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        method_title.pack(pady=(15, 10))

        self.sort_method_var = ctk.StringVar(value="manual")

        manual_radio = ctk.CTkRadioButton(
            method_frame,
            text="🖱️ Manual (drag and drop)",
            variable=self.sort_method_var,
            value="manual"
        )
        manual_radio.pack(anchor="w", padx=15, pady=2)

        asc_radio = ctk.CTkRadioButton(
            method_frame,
            text="⬆️ Ascending order",
            variable=self.sort_method_var,
            value="ascending"
        )
        asc_radio.pack(anchor="w", padx=15, pady=2)

        desc_radio = ctk.CTkRadioButton(
            method_frame,
            text="⬇️ Descending order",
            variable=self.sort_method_var,
            value="descending"
        )
        desc_radio.pack(anchor="w", padx=15, pady=2)

        reverse_radio = ctk.CTkRadioButton(
            method_frame,
            text="🔄 Reverse current order",
            variable=self.sort_method_var,
            value="reverse"
        )
        reverse_radio.pack(anchor="w", padx=15, pady=(2, 15))

        # Apply sort button
        apply_sort_btn = ctk.CTkButton(
            right_panel,
            text="🔄 Apply Sort",
            command=self._apply_sort,
            height=40,
            corner_radius=10
        )
        apply_sort_btn.pack(fill="x", padx=20, pady=10)

        # Output file selection
        output_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        output_frame.pack(fill="x", padx=20, pady=10)

        output_title = ctk.CTkLabel(
            output_frame,
            text="💾 Output File",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        output_title.pack(pady=(15, 10))

        self.sort_output_var = ctk.StringVar()
        self.sort_output_entry = ctk.CTkEntry(
            output_frame,
            textvariable=self.sort_output_var,
            placeholder_text="Select output location...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.sort_output_entry.pack(fill="x", padx=15, pady=5)

        browse_sort_output_btn = ctk.CTkButton(
            output_frame,
            text="📂 Browse",
            command=self._select_sort_output,
            height=35,
            width=120,
            corner_radius=8
        )
        browse_sort_output_btn.pack(pady=(5, 15))

        # Sort button
        self.sort_button = ctk.CTkButton(
            right_panel,
            text="🔀 Save Sorted PDF",
            command=self._sort_pages,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=12
        )
        self.sort_button.pack(fill="x", padx=20, pady=20)

    def _create_crop_tab(self):
        """Create the modern crop pages tab."""
        crop_tab = self.tabview.add("✂️ Crop Pages")

        # Configure grid
        crop_tab.grid_columnconfigure(0, weight=2)
        crop_tab.grid_columnconfigure(1, weight=1)
        crop_tab.grid_rowconfigure(0, weight=1)

        # Left panel - PDF preview
        left_panel = ctk.CTkFrame(crop_tab, corner_radius=15)
        left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=10)

        # PDF file selection
        file_frame = ctk.CTkFrame(left_panel, corner_radius=10)
        file_frame.pack(fill="x", padx=20, pady=20)

        file_title = ctk.CTkLabel(
            file_frame,
            text="📂 Select PDF File",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title.pack(pady=(15, 10))

        pdf_select_frame = ctk.CTkFrame(file_frame, corner_radius=8)
        pdf_select_frame.pack(fill="x", padx=15, pady=(0, 15))

        self.crop_pdf_var = ctk.StringVar()
        self.crop_pdf_entry = ctk.CTkEntry(
            pdf_select_frame,
            textvariable=self.crop_pdf_var,
            placeholder_text="No PDF file selected...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.crop_pdf_entry.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)

        browse_crop_btn = ctk.CTkButton(
            pdf_select_frame,
            text="📂",
            command=self._select_crop_pdf,
            width=40,
            height=35,
            corner_radius=8
        )
        browse_crop_btn.pack(side="right", padx=(5, 10), pady=10)

        # Page preview
        preview_title = ctk.CTkLabel(
            left_panel,
            text="👁️ Page Preview",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        preview_title.pack(pady=(10, 10))

        self.crop_preview_frame = ctk.CTkFrame(left_panel, corner_radius=10)
        self.crop_preview_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        preview_placeholder = ctk.CTkLabel(
            self.crop_preview_frame,
            text="📄\nPage preview will appear here\nafter selecting a PDF file",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        preview_placeholder.pack(expand=True)

        # Right panel - crop options
        right_panel = ctk.CTkFrame(crop_tab, corner_radius=15)
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=10)

        # Title
        crop_title = ctk.CTkLabel(
            right_panel,
            text="✂️ Crop Options",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        crop_title.pack(pady=(20, 20))

        # Page selection
        page_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        page_frame.pack(fill="x", padx=20, pady=10)

        page_title = ctk.CTkLabel(
            page_frame,
            text="📄 Page Selection",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        page_title.pack(pady=(15, 10))

        page_label = ctk.CTkLabel(page_frame, text="Page Number:")
        page_label.pack(pady=(0, 5))

        self.crop_page_var = ctk.StringVar(value="1")
        self.crop_page_entry = ctk.CTkEntry(
            page_frame,
            textvariable=self.crop_page_var,
            height=35,
            width=100,
            corner_radius=8
        )
        self.crop_page_entry.pack(pady=(0, 15))

        # Crop coordinates
        coords_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        coords_frame.pack(fill="x", padx=20, pady=10)

        coords_title = ctk.CTkLabel(
            coords_frame,
            text="📐 Crop Coordinates",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        coords_title.pack(pady=(15, 10))

        # Coordinate inputs in a grid
        coords_grid = ctk.CTkFrame(coords_frame, corner_radius=5)
        coords_grid.pack(fill="x", padx=15, pady=(0, 15))

        coords_grid.grid_columnconfigure((0, 1), weight=1)

        # Left (X0)
        ctk.CTkLabel(coords_grid, text="Left (X0):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.crop_x0_var = ctk.StringVar(value="0")
        ctk.CTkEntry(coords_grid, textvariable=self.crop_x0_var, height=30).grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # Top (Y0)
        ctk.CTkLabel(coords_grid, text="Top (Y0):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.crop_y0_var = ctk.StringVar(value="0")
        ctk.CTkEntry(coords_grid, textvariable=self.crop_y0_var, height=30).grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Right (X1)
        ctk.CTkLabel(coords_grid, text="Right (X1):").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.crop_x1_var = ctk.StringVar(value="612")
        ctk.CTkEntry(coords_grid, textvariable=self.crop_x1_var, height=30).grid(row=2, column=1, padx=5, pady=5, sticky="ew")

        # Bottom (Y1)
        ctk.CTkLabel(coords_grid, text="Bottom (Y1):").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.crop_y1_var = ctk.StringVar(value="792")
        ctk.CTkEntry(coords_grid, textvariable=self.crop_y1_var, height=30).grid(row=3, column=1, padx=5, pady=5, sticky="ew")

        # Preset crops
        preset_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        preset_frame.pack(fill="x", padx=20, pady=10)

        preset_title = ctk.CTkLabel(
            preset_frame,
            text="🎯 Quick Presets",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        preset_title.pack(pady=(15, 10))

        preset_grid = ctk.CTkFrame(preset_frame, corner_radius=5)
        preset_grid.pack(fill="x", padx=15, pady=(0, 15))

        preset_grid.grid_columnconfigure((0, 1), weight=1)

        ctk.CTkButton(preset_grid, text="⬆️ Top Half", command=lambda: self._set_crop_preset("top"), height=30).grid(row=0, column=0, padx=2, pady=2, sticky="ew")
        ctk.CTkButton(preset_grid, text="⬇️ Bottom Half", command=lambda: self._set_crop_preset("bottom"), height=30).grid(row=0, column=1, padx=2, pady=2, sticky="ew")
        ctk.CTkButton(preset_grid, text="⬅️ Left Half", command=lambda: self._set_crop_preset("left"), height=30).grid(row=1, column=0, padx=2, pady=2, sticky="ew")
        ctk.CTkButton(preset_grid, text="➡️ Right Half", command=lambda: self._set_crop_preset("right"), height=30).grid(row=1, column=1, padx=2, pady=2, sticky="ew")

        # Output file selection
        output_frame = ctk.CTkFrame(right_panel, corner_radius=10)
        output_frame.pack(fill="x", padx=20, pady=10)

        output_title = ctk.CTkLabel(
            output_frame,
            text="💾 Output File",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        output_title.pack(pady=(15, 10))

        self.crop_output_var = ctk.StringVar()
        self.crop_output_entry = ctk.CTkEntry(
            output_frame,
            textvariable=self.crop_output_var,
            placeholder_text="Select output location...",
            height=35,
            corner_radius=8,
            state="readonly"
        )
        self.crop_output_entry.pack(fill="x", padx=15, pady=5)

        browse_crop_output_btn = ctk.CTkButton(
            output_frame,
            text="📂 Browse",
            command=self._select_crop_output,
            height=35,
            width=120,
            corner_radius=8
        )
        browse_crop_output_btn.pack(pady=(5, 15))

        # Crop button
        self.crop_button = ctk.CTkButton(
            right_panel,
            text="✂️ Crop Pages",
            command=self._crop_pages,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=12
        )
        self.crop_button.pack(fill="x", padx=20, pady=20)

    # Event handlers and utility methods
    def _update_status(self, message: str, status_type: str = "info"):
        """Update status bar with modern styling."""
        icons = {
            "info": "🟢",
            "warning": "🟡",
            "error": "🔴",
            "processing": "🔄"
        }
        icon = icons.get(status_type, "🟢")
        self.status_label.configure(text=f"{icon} {message}")

    def _show_create_menu(self):
        """Show modern create menu."""
        menu = ctk.CTkToplevel(self.root)
        menu.title("Create PDF")
        menu.geometry("400x300")
        menu.transient(self.root)
        menu.grab_set()

        # Center the menu
        menu.geometry("+%d+%d" % (self.root.winfo_rootx() + 200, self.root.winfo_rooty() + 150))

        # Main frame
        main_frame = ctk.CTkFrame(menu, corner_radius=15)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="📝 Create New PDF",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=(20, 30))

        # Options
        text_btn = ctk.CTkButton(
            main_frame,
            text="📝 Create from Text",
            command=lambda: [menu.destroy(), self._create_from_text()],
            height=50,
            font=ctk.CTkFont(size=14),
            corner_radius=10
        )
        text_btn.pack(fill="x", padx=30, pady=10)

        image_btn = ctk.CTkButton(
            main_frame,
            text="🖼️ Create from Images",
            command=lambda: [menu.destroy(), self._create_from_images()],
            height=50,
            font=ctk.CTkFont(size=14),
            corner_radius=10
        )
        image_btn.pack(fill="x", padx=30, pady=10)

        cancel_btn = ctk.CTkButton(
            main_frame,
            text="❌ Cancel",
            command=menu.destroy,
            height=40,
            font=ctk.CTkFont(size=12),
            corner_radius=8,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        cancel_btn.pack(fill="x", padx=30, pady=(20, 20))

    # Event handlers for merge tab
    def _on_merge_files_dropped(self, files: List[str]):
        """Handle files dropped on merge tab."""
        valid_files = [f for f in files if validate_pdf_file(f)]
        if valid_files:
            self.merge_file_list.add_files(valid_files)
            self._update_status(f"Added {len(valid_files)} PDF files", "info")
        else:
            show_error("Invalid Files", "No valid PDF files were found in the selection.")

    def _add_merge_files(self):
        """Add files to merge list via file dialog."""
        from utils import select_input_files
        files = select_input_files("Select PDF Files to Merge")
        if files:
            self.merge_file_list.add_files(files)
            self._update_status(f"Added {len(files)} PDF files", "info")

    def _on_merge_selection_change(self, selected_files: List[str]):
        """Handle selection change in merge file list."""
        self._update_merge_info(selected_files)

    def _update_merge_info(self, files: List[str]):
        """Update file information display for merge tab."""
        self.merge_info_text.delete("1.0", "end")

        if not files:
            self.merge_info_text.insert("1.0", "📋 No files selected\n\nDrag and drop PDF files or click 'Add Files' to get started.")
        else:
            total_pages = 0
            total_size = 0

            info_text = "📊 File Information\n" + "="*30 + "\n\n"

            for i, file_path in enumerate(files, 1):
                info = get_pdf_info(file_path)
                if info:
                    filename = os.path.basename(file_path)
                    info_text += f"{i}. {filename}\n"
                    info_text += f"   📄 Pages: {info['page_count']}\n"
                    info_text += f"   💾 Size: {format_file_size(info['file_size'])}\n"
                    if info['title']:
                        info_text += f"   📝 Title: {info['title']}\n"
                    info_text += "\n"

                    total_pages += info['page_count']
                    total_size += info['file_size']

            if len(files) > 1:
                info_text += "="*30 + "\n"
                info_text += f"📊 Total Pages: {total_pages}\n"
                info_text += f"💾 Total Size: {format_file_size(total_size)}"

            self.merge_info_text.insert("1.0", info_text)

    def _select_merge_output(self):
        """Select output file for merge operation."""
        output_file = select_output_file("merged_document.pdf", "Save Merged PDF As")
        if output_file:
            self.merge_output_var.set(output_file)

    def _merge_pdfs(self):
        """Perform PDF merge operation."""
        files = self.merge_file_list.get_all_files()
        output_path = self.merge_output_var.get()

        if not files:
            show_error("No Files", "Please add PDF files to merge.")
            return

        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Validate all files
        invalid_files = [f for f in files if not validate_pdf_file(f)]
        if invalid_files:
            show_error("Invalid Files", f"The following files are not valid PDFs:\n" +
                      "\n".join([os.path.basename(f) for f in invalid_files]))
            return

        # Perform merge in separate thread
        def merge_thread():
            progress_dialog = SimpleProgressDialog(self.root, "Merging PDFs...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                self._update_status("Merging PDFs...", "processing")
                success = self.pdf_processor.merge_pdfs(files, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()

                    if success:
                        show_info("Success", f"PDFs merged successfully!\nOutput: {output_path}")
                        self._update_status("Merge completed successfully", "info")
                    else:
                        show_error("Error", "Failed to merge PDFs. Please check the files and try again.")
                        self._update_status("Merge failed", "error")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()
                    show_error("Error", f"An error occurred during merge: {str(e)}")
                    self._update_status("Merge failed", "error")

        threading.Thread(target=merge_thread, daemon=True).start()

    # Event handlers for extract tab
    def _select_extract_pdf(self):
        """Select PDF file for extraction."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.extract_pdf_var.set(file_path)
                self._load_extract_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_extract_thumbnails(self, file_path: str):
        """Load thumbnails for extract tab."""
        def load_thread():
            self._update_status("Loading PDF pages...", "processing")
            if self.pdf_processor.load_pdf(file_path):
                page_count = self.pdf_processor.get_page_count()

                # Clear existing thumbnails
                self.extract_thumbnails.clear_thumbnails()

                # Load thumbnails
                for page_num in range(page_count):
                    thumbnail = self.pdf_processor.get_page_thumbnail(page_num, 0.3)
                    if thumbnail:
                        self.extract_thumbnails.add_thumbnail(page_num, thumbnail)

                self._update_status(f"Loaded {page_count} pages", "info")
            else:
                show_error("Error", "Failed to load PDF file.")
                self._update_status("Failed to load PDF", "error")

        threading.Thread(target=load_thread, daemon=True).start()

    def _on_extract_selection_change(self, selected_pages: set):
        """Handle page selection change in extract tab."""
        self.selected_pages = selected_pages
        count = len(selected_pages)
        if count == 0:
            self.extract_info_var.set("📄 No pages selected")
        elif count == 1:
            page_num = list(selected_pages)[0] + 1
            self.extract_info_var.set(f"📄 Selected: Page {page_num}")
        else:
            page_list = sorted([p + 1 for p in selected_pages])
            self.extract_info_var.set(f"📄 Selected: {count} pages\n({', '.join(map(str, page_list[:5]))}{'...' if count > 5 else ''})")

    def _select_extract_output(self):
        """Select output file for extract operation."""
        output_file = select_output_file("extracted_pages.pdf", "Save Extracted Pages As")
        if output_file:
            self.extract_output_var.set(output_file)

    def _extract_pages(self):
        """Perform page extraction operation."""
        if not self.selected_pages:
            show_error("No Pages", "Please select pages to extract.")
            return

        output_path = self.extract_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Perform extraction in separate thread
        def extract_thread():
            progress_dialog = SimpleProgressDialog(self.root, "Extracting Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                self._update_status("Extracting pages...", "processing")
                page_list = sorted(list(self.selected_pages))
                success = self.pdf_processor.extract_pages(page_list, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()

                    if success:
                        show_info("Success", f"Pages extracted successfully!\nOutput: {output_path}")
                        self._update_status("Extraction completed successfully", "info")
                    else:
                        show_error("Error", "Failed to extract pages. Please try again.")
                        self._update_status("Extraction failed", "error")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()
                    show_error("Error", f"An error occurred during extraction: {str(e)}")
                    self._update_status("Extraction failed", "error")

        threading.Thread(target=extract_thread, daemon=True).start()

    # Event handlers for sort tab
    def _select_sort_pdf(self):
        """Select PDF file for sorting."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.sort_pdf_var.set(file_path)
                self._load_sort_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_sort_thumbnails(self, file_path: str):
        """Load thumbnails for sort tab."""
        def load_thread():
            self._update_status("Loading PDF pages for sorting...", "processing")
            if self.pdf_processor.load_pdf(file_path):
                page_count = self.pdf_processor.get_page_count()

                # Clear existing thumbnails
                self.sort_thumbnails.clear_thumbnails()

                # Load thumbnails (non-selectable for sorting)
                for page_num in range(page_count):
                    thumbnail = self.pdf_processor.get_page_thumbnail(page_num, 0.3)
                    if thumbnail:
                        self.sort_thumbnails.add_thumbnail(page_num, thumbnail, selectable=False)

                self._update_status(f"Loaded {page_count} pages for sorting", "info")
            else:
                show_error("Error", "Failed to load PDF file.")
                self._update_status("Failed to load PDF", "error")

        threading.Thread(target=load_thread, daemon=True).start()

    def _apply_sort(self):
        """Apply selected sort method."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        method = self.sort_method_var.get()
        page_count = self.pdf_processor.get_page_count()

        if method == "ascending":
            show_info("Sort Applied", "✅ Pages are already in ascending order.")
        elif method == "descending":
            show_info("Sort Applied", "✅ Pages will be sorted in descending order when saved.")
        elif method == "reverse":
            show_info("Sort Applied", "✅ Pages will be reversed when saved.")
        else:
            show_info("Manual Sort", "🖱️ Use drag and drop to manually reorder pages (feature to be implemented).")

    def _select_sort_output(self):
        """Select output file for sort operation."""
        output_file = select_output_file("sorted_document.pdf", "Save Sorted PDF As")
        if output_file:
            self.sort_output_var.set(output_file)

    def _sort_pages(self):
        """Perform page sorting operation."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        output_path = self.sort_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        method = self.sort_method_var.get()
        page_count = self.pdf_processor.get_page_count()

        # Determine page order based on sort method
        if method == "ascending":
            page_order = list(range(page_count))
        elif method == "descending":
            page_order = list(range(page_count - 1, -1, -1))
        elif method == "reverse":
            page_order = list(range(page_count - 1, -1, -1))
        else:  # manual
            page_order = list(range(page_count))

        # Perform sorting in separate thread
        def sort_thread():
            progress_dialog = SimpleProgressDialog(self.root, "Sorting Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                self._update_status("Sorting pages...", "processing")
                success = self.pdf_processor.sort_pages(page_order, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()

                    if success:
                        show_info("Success", f"Pages sorted successfully!\nOutput: {output_path}")
                        self._update_status("Sort completed successfully", "info")
                    else:
                        show_error("Error", "Failed to sort pages. Please try again.")
                        self._update_status("Sort failed", "error")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()
                    show_error("Error", f"An error occurred during sorting: {str(e)}")
                    self._update_status("Sort failed", "error")

        threading.Thread(target=sort_thread, daemon=True).start()

    # Event handlers for crop tab
    def _select_crop_pdf(self):
        """Select PDF file for cropping."""
        from utils import select_input_files
        files = select_input_files("Select PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                self.crop_pdf_var.set(file_path)
                self._load_crop_pdf(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _load_crop_pdf(self, file_path: str):
        """Load PDF for cropping."""
        self._update_status("Loading PDF for cropping...", "processing")
        if self.pdf_processor.load_pdf(file_path):
            page_count = self.pdf_processor.get_page_count()
            self._update_status(f"Loaded PDF with {page_count} pages", "info")
        else:
            show_error("Error", "Failed to load PDF file.")
            self._update_status("Failed to load PDF", "error")

    def _set_crop_preset(self, preset: str):
        """Set crop coordinates based on preset."""
        # Default page size (US Letter: 612 x 792 points)
        width, height = 612, 792

        if preset == "top":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height // 2))
        elif preset == "bottom":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set(str(height // 2))
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height))
        elif preset == "left":
            self.crop_x0_var.set("0")
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width // 2))
            self.crop_y1_var.set(str(height))
        elif preset == "right":
            self.crop_x0_var.set(str(width // 2))
            self.crop_y0_var.set("0")
            self.crop_x1_var.set(str(width))
            self.crop_y1_var.set(str(height))

    def _select_crop_output(self):
        """Select output file for crop operation."""
        output_file = select_output_file("cropped_document.pdf", "Save Cropped PDF As")
        if output_file:
            self.crop_output_var.set(output_file)

    def _crop_pages(self):
        """Perform page cropping operation."""
        if not self.pdf_processor.current_doc:
            show_error("No PDF", "Please select a PDF file first.")
            return

        output_path = self.crop_output_var.get()
        if not output_path:
            show_error("No Output File", "Please select an output file location.")
            return

        # Get crop coordinates
        try:
            x0 = float(self.crop_x0_var.get())
            y0 = float(self.crop_y0_var.get())
            x1 = float(self.crop_x1_var.get())
            y1 = float(self.crop_y1_var.get())

            if x0 >= x1 or y0 >= y1:
                show_error("Invalid Coordinates", "Invalid crop coordinates. Please check the values.")
                return

            crop_rect = (x0, y0, x1, y1)
        except ValueError:
            show_error("Invalid Coordinates", "Please enter valid numeric coordinates.")
            return

        # Get page number
        try:
            page_num = int(self.crop_page_var.get()) - 1
            page_count = self.pdf_processor.get_page_count()
            if not (0 <= page_num < page_count):
                show_error("Invalid Page", "Invalid page number.")
                return
            page_numbers = [page_num]
        except ValueError:
            show_error("Invalid Page", "Please enter a valid page number.")
            return

        # Perform cropping in separate thread
        def crop_thread():
            progress_dialog = SimpleProgressDialog(self.root, "Cropping Pages...")

            def progress_callback(progress, status):
                if not progress_dialog.cancelled:
                    progress_dialog.update_progress(progress, status)

            try:
                self._update_status("Cropping pages...", "processing")
                success = self.pdf_processor.crop_pages(page_numbers, crop_rect, output_path, progress_callback)

                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()

                    if success:
                        show_info("Success", f"Pages cropped successfully!\nOutput: {output_path}")
                        self._update_status("Crop completed successfully", "info")
                    else:
                        show_error("Error", "Failed to crop pages. Please try again.")
                        self._update_status("Crop failed", "error")
            except Exception as e:
                if not progress_dialog.cancelled:
                    progress_dialog.safe_destroy()
                    show_error("Error", f"An error occurred during cropping: {str(e)}")
                    self._update_status("Crop failed", "error")

        threading.Thread(target=crop_thread, daemon=True).start()

    # Additional menu handlers
    def _open_pdf(self):
        """Open a PDF file."""
        from utils import select_input_files
        files = select_input_files("Open PDF File")
        if files:
            file_path = files[0]
            if validate_pdf_file(file_path):
                # Switch to extract tab and load the file
                self.tabview.set("📄 Extract Pages")
                self.extract_pdf_var.set(file_path)
                self._load_extract_thumbnails(file_path)
            else:
                show_error("Invalid File", "The selected file is not a valid PDF.")

    def _create_from_text(self):
        """Create PDF from text input with modern dialog."""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Create PDF from Text")
        dialog.geometry("700x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 50))

        # Main frame
        main_frame = ctk.CTkFrame(dialog, corner_radius=15)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="📝 Create PDF from Text",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=(20, 20))

        # Text input area
        text_label = ctk.CTkLabel(
            main_frame,
            text="Enter your text content:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        text_label.pack(anchor="w", padx=20, pady=(0, 10))

        text_area = ctk.CTkTextbox(
            main_frame,
            corner_radius=10,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        text_area.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # Options frame
        options_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        options_frame.pack(fill="x", padx=20, pady=(0, 20))

        font_label = ctk.CTkLabel(options_frame, text="Font Size:")
        font_label.pack(side="left", padx=(15, 5), pady=15)

        font_size_var = ctk.StringVar(value="12")
        font_size_entry = ctk.CTkEntry(
            options_frame,
            textvariable=font_size_var,
            width=60,
            height=30
        )
        font_size_entry.pack(side="left", padx=5, pady=15)

        # Buttons frame
        button_frame = ctk.CTkFrame(main_frame, corner_radius=10)
        button_frame.pack(fill="x", padx=20, pady=(0, 20))

        def create_pdf():
            text_content = text_area.get("1.0", "end").strip()
            if not text_content:
                show_error("No Text", "Please enter some text content.")
                return

            output_file = select_output_file("text_document.pdf", "Save PDF As")
            if output_file:
                try:
                    font_size = int(font_size_var.get())
                    success = self.pdf_processor.create_pdf_from_text(text_content, output_file, font_size)

                    if success:
                        show_info("Success", f"PDF created successfully!\nOutput: {output_file}")
                        self._update_status("PDF created from text", "info")
                        dialog.destroy()
                    else:
                        show_error("Error", "Failed to create PDF from text.")
                except ValueError:
                    show_error("Invalid Font Size", "Please enter a valid font size.")

        create_btn = ctk.CTkButton(
            button_frame,
            text="📄 Create PDF",
            command=create_pdf,
            height=40,
            corner_radius=10
        )
        create_btn.pack(side="right", padx=(5, 15), pady=15)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            height=40,
            corner_radius=10,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        cancel_btn.pack(side="right", padx=5, pady=15)

    def _create_from_images(self):
        """Create PDF from image files."""
        # Select image files
        image_files = filedialog.askopenfilenames(
            title="Select Image Files",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )

        if not image_files:
            return

        output_file = select_output_file("image_document.pdf", "Save PDF As")
        if output_file:
            # Create PDF in separate thread
            def create_thread():
                progress_dialog = SimpleProgressDialog(self.root, "Creating PDF from Images...")

                def progress_callback(progress, status):
                    if not progress_dialog.cancelled:
                        progress_dialog.update_progress(progress, status)

                try:
                    self._update_status("Creating PDF from images...", "processing")
                    success = self.pdf_processor.create_pdf_from_images(list(image_files), output_file, progress_callback)

                    if not progress_dialog.cancelled:
                        progress_dialog.safe_destroy()

                        if success:
                            show_info("Success", f"PDF created successfully!\nOutput: {output_file}")
                            self._update_status("PDF created from images", "info")
                        else:
                            show_error("Error", "Failed to create PDF from images.")
                            self._update_status("PDF creation failed", "error")
                except Exception as e:
                    if not progress_dialog.cancelled:
                        progress_dialog.safe_destroy()
                        show_error("Error", f"An error occurred: {str(e)}")
                        self._update_status("PDF creation failed", "error")

            threading.Thread(target=create_thread, daemon=True).start()

    def _on_closing(self):
        """Handle application closing."""
        # Close any open PDF documents
        self.pdf_processor.close_current_pdf()

        # Destroy the main window
        self.root.destroy()

    def run(self):
        """Start the application."""
        self.root.mainloop()


def main():
    """Main entry point for modern application."""
    try:
        app = ModernPDFManipulatorApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application: {e}")


if __name__ == "__main__":
    main()
