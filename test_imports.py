"""
Test script to check which imports are causing issues.
"""

import sys
import traceback


def test_import(module_name, import_statement):
    """Test a specific import."""
    try:
        print(f"Testing: {import_statement}")
        exec(import_statement)
        print(f"✅ {module_name} - OK")
        return True
    except Exception as e:
        print(f"❌ {module_name} - FAILED: {e}")
        return False


def main():
    """Test all imports used in modern_main.py."""
    print("Testing imports for modern_main.py")
    print("=" * 50)
    
    imports_to_test = [
        ("customtkinter", "import customtkinter as ctk"),
        ("tkinter", "import tkinter as tk"),
        ("tkinter.messagebox", "from tkinter import messagebox, filedialog"),
        ("threading", "import threading"),
        ("os", "import os"),
        ("typing", "from typing import List, Optional"),
        ("tkinterdnd2", "from tkinterdnd2 import TkinterDnD"),
        ("pdf_processor", "from pdf_processor import PDFProcessor"),
        ("modern_gui_components", "from modern_gui_components import ModernDragDropFrame, SimpleProgressDialog, ModernPageThumbnailFrame, ModernFileListFrame, ModernSidebar"),
        ("simple_welcome", "from simple_welcome import show_simple_welcome"),
        ("utils", "from utils import validate_pdf_file, get_pdf_info, format_file_size, select_output_file, show_error, show_info, center_window, parse_page_range"),
    ]
    
    failed_imports = []
    
    for module_name, import_statement in imports_to_test:
        if not test_import(module_name, import_statement):
            failed_imports.append(module_name)
        print()
    
    print("=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if failed_imports:
        print(f"❌ Failed imports: {', '.join(failed_imports)}")
        print("\nThe app won't work until these issues are resolved.")
        
        # Suggest solutions
        if "tkinterdnd2" in failed_imports:
            print("\nTo fix tkinterdnd2:")
            print("  pip install tkinterdnd2")
        
        if "pdf_processor" in failed_imports:
            print("\nTo fix pdf_processor:")
            print("  Make sure pdf_processor.py exists in the current directory")
        
        if "modern_gui_components" in failed_imports:
            print("\nTo fix modern_gui_components:")
            print("  Make sure modern_gui_components.py exists in the current directory")
        
        if "simple_welcome" in failed_imports:
            print("\nTo fix simple_welcome:")
            print("  Make sure simple_welcome.py exists in the current directory")
        
        if "utils" in failed_imports:
            print("\nTo fix utils:")
            print("  Make sure utils.py exists in the current directory")
    
    else:
        print("✅ All imports successful!")
        print("The issue might be elsewhere. Try running:")
        print("  python simple_modern_app.py")
        print("  or")
        print("  python minimal_app.py")


if __name__ == "__main__":
    main()
