"""
Core PDF processing functionality using PyMuPDF (fitz).
"""

import fitz  # PyMuPDF
import os
from typing import List, Optional, Tuple, Union
from PIL import Image
import io


class PDFProcessor:
    """Main class for PDF manipulation operations."""
    
    def __init__(self):
        self.current_doc = None
        self.current_file_path = None
    
    def load_pdf(self, file_path: str) -> bool:
        """
        Load a PDF file for processing.
        
        Args:
            file_path (str): Path to the PDF file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.current_doc:
                self.current_doc.close()
            
            self.current_doc = fitz.open(file_path)
            self.current_file_path = file_path
            return True
        except Exception as e:
            print(f"Error loading PDF: {e}")
            return False
    
    def close_current_pdf(self):
        """Close the currently loaded PDF."""
        if self.current_doc:
            self.current_doc.close()
            self.current_doc = None
            self.current_file_path = None
    
    def get_page_count(self) -> int:
        """Get the number of pages in the current PDF."""
        return len(self.current_doc) if self.current_doc else 0
    
    def get_page_thumbnail(self, page_num: int, zoom: float = 1.0) -> Optional[Image.Image]:
        """
        Get a thumbnail image of a specific page.
        
        Args:
            page_num (int): Page number (0-indexed)
            zoom (float): Zoom factor for the thumbnail
            
        Returns:
            PIL.Image: Thumbnail image or None if error
        """
        try:
            if not self.current_doc or page_num >= len(self.current_doc):
                return None
            
            page = self.current_doc[page_num]
            mat = fitz.Matrix(zoom, zoom)
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("ppm")
            img = Image.open(io.BytesIO(img_data))
            return img
        except Exception as e:
            print(f"Error getting page thumbnail: {e}")
            return None
    
    def merge_pdfs(self, input_files: List[str], output_path: str, 
                   progress_callback=None) -> bool:
        """
        Merge multiple PDF files into one.
        
        Args:
            input_files (List[str]): List of input PDF file paths
            output_path (str): Output file path
            progress_callback: Optional callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            output_doc = fitz.open()
            total_files = len(input_files)
            
            for i, file_path in enumerate(input_files):
                if progress_callback:
                    progress_callback(i / total_files * 100, f"Processing {os.path.basename(file_path)}")
                
                input_doc = fitz.open(file_path)
                output_doc.insert_pdf(input_doc)
                input_doc.close()
            
            output_doc.save(output_path)
            output_doc.close()
            
            if progress_callback:
                progress_callback(100, "Merge completed")
            
            return True
        except Exception as e:
            print(f"Error merging PDFs: {e}")
            return False
    
    def extract_pages(self, page_numbers: List[int], output_path: str,
                     progress_callback=None) -> bool:
        """
        Extract specific pages and save as a new PDF.
        
        Args:
            page_numbers (List[int]): List of page numbers to extract (0-indexed)
            output_path (str): Output file path
            progress_callback: Optional callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.current_doc:
                return False
            
            output_doc = fitz.open()
            total_pages = len(page_numbers)
            
            for i, page_num in enumerate(page_numbers):
                if progress_callback:
                    progress_callback(i / total_pages * 100, f"Extracting page {page_num + 1}")
                
                if 0 <= page_num < len(self.current_doc):
                    output_doc.insert_pdf(self.current_doc, from_page=page_num, to_page=page_num)
            
            output_doc.save(output_path)
            output_doc.close()
            
            if progress_callback:
                progress_callback(100, "Extraction completed")
            
            return True
        except Exception as e:
            print(f"Error extracting pages: {e}")
            return False
    
    def crop_pages(self, page_numbers: List[int], crop_rect: Tuple[float, float, float, float],
                   output_path: str, progress_callback=None) -> bool:
        """
        Crop specified pages and save as a new PDF.
        
        Args:
            page_numbers (List[int]): List of page numbers to crop (0-indexed)
            crop_rect (Tuple): Crop rectangle (x0, y0, x1, y1) in points
            output_path (str): Output file path
            progress_callback: Optional callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.current_doc:
                return False
            
            output_doc = fitz.open()
            total_pages = len(page_numbers)
            
            for i, page_num in enumerate(page_numbers):
                if progress_callback:
                    progress_callback(i / total_pages * 100, f"Cropping page {page_num + 1}")
                
                if 0 <= page_num < len(self.current_doc):
                    page = self.current_doc[page_num]
                    
                    # Create new page with cropped content
                    new_page = output_doc.new_page(width=crop_rect[2] - crop_rect[0],
                                                 height=crop_rect[3] - crop_rect[1])
                    
                    # Copy the cropped area
                    new_page.show_pdf_page(fitz.Rect(0, 0, crop_rect[2] - crop_rect[0],
                                                    crop_rect[3] - crop_rect[1]),
                                          self.current_doc, page_num,
                                          clip=fitz.Rect(crop_rect))
            
            output_doc.save(output_path)
            output_doc.close()
            
            if progress_callback:
                progress_callback(100, "Cropping completed")
            
            return True
        except Exception as e:
            print(f"Error cropping pages: {e}")
            return False
    
    def create_pdf_from_text(self, text: str, output_path: str, 
                           font_size: int = 12, font_name: str = "helv") -> bool:
        """
        Create a PDF from text content.
        
        Args:
            text (str): Text content
            output_path (str): Output file path
            font_size (int): Font size
            font_name (str): Font name
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            doc = fitz.open()
            page = doc.new_page()
            
            # Insert text with word wrapping
            text_rect = fitz.Rect(50, 50, page.rect.width - 50, page.rect.height - 50)
            page.insert_textbox(text_rect, text, fontsize=font_size, fontname=font_name)
            
            doc.save(output_path)
            doc.close()
            return True
        except Exception as e:
            print(f"Error creating PDF from text: {e}")
            return False
    
    def create_pdf_from_images(self, image_paths: List[str], output_path: str,
                             progress_callback=None) -> bool:
        """
        Create a PDF from image files.
        
        Args:
            image_paths (List[str]): List of image file paths
            output_path (str): Output file path
            progress_callback: Optional callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            doc = fitz.open()
            total_images = len(image_paths)
            
            for i, img_path in enumerate(image_paths):
                if progress_callback:
                    progress_callback(i / total_images * 100, f"Processing {os.path.basename(img_path)}")
                
                # Open image and get dimensions
                img = Image.open(img_path)
                img_width, img_height = img.size
                
                # Create page with image dimensions (converted to points)
                page = doc.new_page(width=img_width * 72 / 96, height=img_height * 72 / 96)
                
                # Insert image
                page.insert_image(page.rect, filename=img_path)
            
            doc.save(output_path)
            doc.close()
            
            if progress_callback:
                progress_callback(100, "PDF creation completed")
            
            return True
        except Exception as e:
            print(f"Error creating PDF from images: {e}")
            return False
    
    def sort_pages(self, page_order: List[int], output_path: str,
                   progress_callback=None) -> bool:
        """
        Sort pages according to specified order and save as new PDF.
        
        Args:
            page_order (List[int]): List of page numbers in desired order (0-indexed)
            output_path (str): Output file path
            progress_callback: Optional callback function for progress updates
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.current_doc:
                return False
            
            output_doc = fitz.open()
            total_pages = len(page_order)
            
            for i, page_num in enumerate(page_order):
                if progress_callback:
                    progress_callback(i / total_pages * 100, f"Sorting page {page_num + 1}")
                
                if 0 <= page_num < len(self.current_doc):
                    output_doc.insert_pdf(self.current_doc, from_page=page_num, to_page=page_num)
            
            output_doc.save(output_path)
            output_doc.close()
            
            if progress_callback:
                progress_callback(100, "Sorting completed")
            
            return True
        except Exception as e:
            print(f"Error sorting pages: {e}")
            return False
