"""
Minimal working version of PDF Manipulator to test basic functionality.
"""

import tkinter as tk
from tkinter import messagebox, filedialog
import os


class MinimalPDFApp:
    """Minimal PDF application for testing."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PDF Manipulator - Minimal Test")
        self.root.geometry("600x400")
        
        # Center window
        self.center_window()
        
        # Create interface
        self.create_interface()
        
        print("Minimal app created successfully")
    
    def center_window(self):
        """Center the window on screen."""
        self.root.update_idletasks()
        width = 600
        height = 400
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """Create minimal interface."""
        # Title
        title_label = tk.Label(
            self.root,
            text="PDF Manipulator Pro - Test Version",
            font=("Arial", 18, "bold"),
            fg="#1f538d"
        )
        title_label.pack(pady=20)
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Application is working!")
        status_label = tk.Label(
            self.root,
            textvariable=self.status_var,
            font=("Arial", 12),
            fg="green"
        )
        status_label.pack(pady=10)
        
        # Test buttons
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=30)
        
        # Test basic functionality
        test_button = tk.Button(
            button_frame,
            text="Test Basic Functions",
            command=self.test_basic,
            font=("Arial", 12),
            bg="#4CAF50",
            fg="white",
            padx=20,
            pady=10
        )
        test_button.pack(pady=5)
        
        # Test file dialog
        file_button = tk.Button(
            button_frame,
            text="Test File Dialog",
            command=self.test_file_dialog,
            font=("Arial", 12),
            bg="#2196F3",
            fg="white",
            padx=20,
            pady=10
        )
        file_button.pack(pady=5)
        
        # Test dependencies
        deps_button = tk.Button(
            button_frame,
            text="Test Dependencies",
            command=self.test_dependencies,
            font=("Arial", 12),
            bg="#FF9800",
            fg="white",
            padx=20,
            pady=10
        )
        deps_button.pack(pady=5)
        
        # Launch full app
        launch_button = tk.Button(
            button_frame,
            text="Launch Full Modern App",
            command=self.launch_full_app,
            font=("Arial", 12, "bold"),
            bg="#1f538d",
            fg="white",
            padx=20,
            pady=10
        )
        launch_button.pack(pady=15)
        
        # Close button
        close_button = tk.Button(
            button_frame,
            text="Close",
            command=self.root.destroy,
            font=("Arial", 10),
            bg="#f44336",
            fg="white",
            padx=20,
            pady=5
        )
        close_button.pack(pady=10)
        
        # Info text
        info_text = tk.Text(self.root, height=8, width=70)
        info_text.pack(pady=20, padx=20, fill="both", expand=True)
        
        info_content = """This is a minimal test version of PDF Manipulator Pro.

If you can see this window and the buttons work, then basic functionality is operational.

Test the buttons above to verify different components:
• Test Basic Functions - Checks if the app responds
• Test File Dialog - Checks if file selection works  
• Test Dependencies - Checks if required modules are available
• Launch Full Modern App - Attempts to start the complete application

If any test fails, you'll see error messages to help diagnose the issue."""
        
        info_text.insert("1.0", info_content)
        info_text.config(state="disabled")
    
    def test_basic(self):
        """Test basic functionality."""
        try:
            self.status_var.set("Testing basic functions...")
            self.root.update()
            
            # Simple test
            result = 2 + 2
            if result == 4:
                self.status_var.set("✅ Basic functions work!")
                messagebox.showinfo("Test Result", "✅ Basic functionality test passed!")
            else:
                self.status_var.set("❌ Basic functions failed!")
                messagebox.showerror("Test Result", "❌ Basic functionality test failed!")
                
        except Exception as e:
            self.status_var.set("❌ Error in basic test!")
            messagebox.showerror("Error", f"Basic test failed: {str(e)}")
    
    def test_file_dialog(self):
        """Test file dialog functionality."""
        try:
            self.status_var.set("Testing file dialog...")
            self.root.update()
            
            # Test file selection
            file_path = filedialog.askopenfilename(
                title="Select any file (this is just a test)",
                filetypes=[("All files", "*.*")]
            )
            
            if file_path:
                self.status_var.set("✅ File dialog works!")
                messagebox.showinfo("Test Result", f"✅ File dialog works!\nSelected: {os.path.basename(file_path)}")
            else:
                self.status_var.set("File dialog cancelled")
                messagebox.showinfo("Test Result", "File dialog test cancelled by user")
                
        except Exception as e:
            self.status_var.set("❌ File dialog failed!")
            messagebox.showerror("Error", f"File dialog test failed: {str(e)}")
    
    def test_dependencies(self):
        """Test if required dependencies are available."""
        try:
            self.status_var.set("Testing dependencies...")
            self.root.update()
            
            results = []
            
            # Test each dependency
            try:
                import customtkinter
                results.append("✅ CustomTkinter")
            except ImportError:
                results.append("❌ CustomTkinter - MISSING")
            
            try:
                import fitz
                results.append("✅ PyMuPDF (fitz)")
            except ImportError:
                results.append("❌ PyMuPDF (fitz) - MISSING")
            
            try:
                from PIL import Image
                results.append("✅ Pillow (PIL)")
            except ImportError:
                results.append("❌ Pillow (PIL) - MISSING")
            
            try:
                import tkinterdnd2
                results.append("✅ tkinterdnd2")
            except ImportError:
                results.append("❌ tkinterdnd2 - MISSING")
            
            # Show results
            result_text = "Dependency Test Results:\n\n" + "\n".join(results)
            
            if "❌" in result_text:
                self.status_var.set("❌ Some dependencies missing!")
                messagebox.showerror("Dependency Test", result_text)
            else:
                self.status_var.set("✅ All dependencies available!")
                messagebox.showinfo("Dependency Test", result_text)
                
        except Exception as e:
            self.status_var.set("❌ Dependency test failed!")
            messagebox.showerror("Error", f"Dependency test failed: {str(e)}")
    
    def launch_full_app(self):
        """Try to launch the full modern application."""
        try:
            self.status_var.set("Launching full application...")
            self.root.update()
            
            # Try to import and run the full app
            if os.path.exists("modern_main.py"):
                import subprocess
                import sys
                
                # Launch in separate process
                subprocess.Popen([sys.executable, "modern_main.py"])
                self.status_var.set("✅ Full app launched!")
                messagebox.showinfo("Launch", "✅ Full application launched successfully!")
                
            else:
                self.status_var.set("❌ modern_main.py not found!")
                messagebox.showerror("Error", "modern_main.py file not found!")
                
        except Exception as e:
            self.status_var.set("❌ Failed to launch full app!")
            messagebox.showerror("Error", f"Failed to launch full app: {str(e)}")
    
    def run(self):
        """Start the minimal application."""
        print("Starting minimal app...")
        self.root.mainloop()
        print("Minimal app closed")


def main():
    """Main entry point."""
    print("PDF Manipulator Pro - Minimal Test Version")
    print("=" * 50)
    
    try:
        app = MinimalPDFApp()
        app.run()
    except Exception as e:
        print(f"Error starting minimal app: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
