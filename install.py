#!/usr/bin/env python3
"""
PDF Manipulator Pro - Installation Script
Automatically installs all required dependencies.
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {command}")
            return True
        else:
            print(f"✗ {command}")
            print(f"Error: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ {command}")
        print(f"Exception: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def install_dependencies():
    """Install all required dependencies."""
    print("\n📦 Installing Dependencies...")
    print("=" * 50)
    
    dependencies = [
        "PyPDF2==3.0.1",
        "PyMuPDF==1.23.14", 
        "Pillow==10.1.0",
        "tkinterdnd2==0.3.0"
    ]
    
    success_count = 0
    for dep in dependencies:
        print(f"\nInstalling {dep}...")
        if run_command(f"pip install {dep}"):
            success_count += 1
    
    print(f"\n📊 Installation Summary: {success_count}/{len(dependencies)} packages installed successfully")
    return success_count == len(dependencies)

def verify_installation():
    """Verify that all packages can be imported."""
    print("\n🔍 Verifying Installation...")
    print("=" * 50)
    
    tests = [
        ("PyPDF2", "import PyPDF2"),
        ("PyMuPDF", "import fitz"),
        ("Pillow", "from PIL import Image, ImageTk"),
        ("tkinterdnd2", "import tkinterdnd2")
    ]
    
    success_count = 0
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"✓ {name} - OK")
            success_count += 1
        except ImportError as e:
            print(f"✗ {name} - Failed: {e}")
    
    print(f"\n📊 Verification Summary: {success_count}/{len(tests)} packages working correctly")
    return success_count == len(tests)

def main():
    """Main installation process."""
    print("🚀 PDF Manipulator Pro - Installation Script")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed! Some packages could not be installed.")
        return 1
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Verification failed! Some packages are not working correctly.")
        return 1
    
    print("\n🎉 Installation completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Run the application: python pdf_manipulator_pro.py")
    print("2. Or use the launcher: python launch_pdf_manipulator.py")
    print("3. Or run directly: python modern_main.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
