"""
Simple, reliable welcome screen using standard Tkinter.
"""

import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk, ImageDraw
import os
from typing import Callable


class SimpleWelcomeScreen:
    """Simple welcome screen using standard Tkinter."""

    def __init__(self, parent, start_callback: Callable = None):
        self.start_callback = start_callback

        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("PDF Manipulator Pro")
        self.window.geometry("700x500")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()

        # Configure colors
        self.bg_color = "#2b2b2b"
        self.card_color = "#3c3c3c"
        self.accent_color = "#1f538d"
        self.text_color = "#ffffff"
        self.secondary_text = "#cccccc"

        self.window.configure(bg=self.bg_color)

        # Center window
        self.center_window()

        # Set icon
        self.set_icon()

        # Create interface
        self.create_interface()

        # Bind close event
        self.window.protocol("WM_DELETE_WINDOW", self._on_close)

    def center_window(self):
        """Center the window on screen."""
        self.window.update_idletasks()
        width = 700
        height = 500
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def set_icon(self):
        """Set window icon if available."""
        try:
            if os.path.exists("icon.ico"):
                self.window.iconbitmap("icon.ico")
        except Exception:
            pass

    def create_logo(self):
        """Create or load logo."""
        try:
            if os.path.exists("logo.png"):
                # Load existing logo
                logo_img = Image.open("logo.png")
                logo_img = logo_img.resize((100, 100), Image.Resampling.LANCZOS)
            else:
                # Create simple logo
                logo_img = Image.new('RGBA', (100, 100), (0, 0, 0, 0))
                draw = ImageDraw.Draw(logo_img)

                # Blue circle background
                draw.ellipse([10, 10, 90, 90], fill="#1f538d", outline="#ffffff", width=3)

                # PDF text
                try:
                    from PIL import ImageFont
                    font = ImageFont.truetype("arial.ttf", 20)
                except:
                    font = ImageFont.load_default()

                draw.text((35, 40), "PDF", fill="white", font=font)

            return ImageTk.PhotoImage(logo_img)

        except Exception as e:
            print(f"Error creating logo: {e}")
            return None

    def create_interface(self):
        """Create the welcome interface."""
        # Main container
        main_frame = tk.Frame(self.window, bg=self.bg_color)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # Header section
        header_frame = tk.Frame(main_frame, bg=self.bg_color)
        header_frame.pack(fill="x", pady=(0, 30))

        # Logo
        logo_image = self.create_logo()
        if logo_image:
            logo_label = tk.Label(header_frame, image=logo_image, bg=self.bg_color)
            logo_label.image = logo_image  # Keep reference
            logo_label.pack(pady=(0, 15))

        # Title
        title_label = tk.Label(
            header_frame,
            text="PDF Manipulator Pro",
            font=("Arial", 28, "bold"),
            fg=self.text_color,
            bg=self.bg_color
        )
        title_label.pack()

        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="Advanced PDF Processing Made Simple",
            font=("Arial", 14),
            fg=self.secondary_text,
            bg=self.bg_color
        )
        subtitle_label.pack(pady=(5, 0))

        # Features section
        features_frame = tk.Frame(main_frame, bg=self.card_color, relief="raised", bd=1)
        features_frame.pack(fill="x", pady=(0, 30))

        features_title = tk.Label(
            features_frame,
            text="✨ Key Features",
            font=("Arial", 16, "bold"),
            fg=self.text_color,
            bg=self.card_color
        )
        features_title.pack(pady=(15, 10))

        # Features list
        features_text = """🔄 Merge multiple PDF files with drag & drop support
📄 Extract specific pages with visual thumbnails
🔀 Sort pages manually or automatically
✂️ Crop pages with custom coordinates or presets
📝 Create PDFs from text content or images
🎨 Modern dark/light theme interface"""

        features_label = tk.Label(
            features_frame,
            text=features_text,
            font=("Arial", 11),
            fg=self.secondary_text,
            bg=self.card_color,
            justify="left"
        )
        features_label.pack(pady=(0, 15), padx=20)

        # Buttons section
        buttons_frame = tk.Frame(main_frame, bg=self.bg_color)
        buttons_frame.pack(fill="x")

        # Start button
        start_button = tk.Button(
            buttons_frame,
            text="🚀 Start Application",
            command=self._start_app,
            font=("Arial", 14, "bold"),
            bg=self.accent_color,
            fg="white",
            relief="flat",
            padx=30,
            pady=12,
            cursor="hand2"
        )
        start_button.pack(pady=(0, 15))

        # Secondary buttons
        secondary_frame = tk.Frame(buttons_frame, bg=self.bg_color)
        secondary_frame.pack()

        demo_button = tk.Button(
            secondary_frame,
            text="📖 View Demo",
            command=self._show_demo,
            font=("Arial", 10),
            bg="#666666",
            fg="white",
            relief="flat",
            padx=15,
            pady=8,
            cursor="hand2"
        )
        demo_button.pack(side="left", padx=5)

        about_button = tk.Button(
            secondary_frame,
            text="ℹ️ About",
            command=self._show_about,
            font=("Arial", 10),
            bg="#666666",
            fg="white",
            relief="flat",
            padx=15,
            pady=8,
            cursor="hand2"
        )
        about_button.pack(side="left", padx=5)

        # Footer
        footer_label = tk.Label(
            main_frame,
            text="© 2024 PDF Manipulator Pro - Professional PDF Processing",
            font=("Arial", 9),
            fg="#888888",
            bg=self.bg_color
        )
        footer_label.pack(side="bottom", pady=(20, 0))

    def _start_app(self):
        """Start the main application."""
        try:
            print("Start button clicked!")
            self.window.destroy()
            if self.start_callback:
                print("Calling start callback...")
                self.start_callback()
        except Exception as e:
            print(f"Error in start app: {e}")
            self.window.destroy()

    def _show_demo(self):
        """Show demo comparison."""
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "demo_comparison.py"])
        except Exception:
            messagebox.showinfo("Demo", "Demo comparison script not found.\nPlease run 'python demo_comparison.py' manually.")

    def _show_about(self):
        """Show about dialog."""
        about_text = """PDF Manipulator Pro v2.0 - Modern UI Edition

A comprehensive PDF manipulation application with contemporary interface.

Features:
• Merge multiple PDF files with drag & drop support
• Extract specific pages with visual thumbnails
• Sort pages manually or automatically
• Crop pages with custom coordinates or presets
• Create PDFs from text content or images
• Modern dark/light theme interface
• Progress tracking for all operations
• Professional styling and user experience

Built with:
• Python 3.7+
• CustomTkinter for modern UI
• PyMuPDF (fitz) for PDF processing
• Pillow for image handling
• tkinterdnd2 for drag & drop

© 2024 PDF Manipulator Pro
Professional PDF processing made simple."""

        messagebox.showinfo("About PDF Manipulator Pro", about_text)

    def _on_close(self):
        """Handle window close event."""
        self.window.destroy()


def show_simple_welcome(parent, start_callback=None):
    """Show the simple welcome screen."""
    welcome = SimpleWelcomeScreen(parent, start_callback)
    return welcome
