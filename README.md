# PDF Manipulator - Advanced PDF Processing Tool

A comprehensive desktop application for PDF manipulation with both **Classic** and **Modern** user interfaces. Built with Python, Tkinter/CustomTkinter, and PyMuPDF (fitz).

## 🎨 Two Beautiful Interfaces

### 🖥️ Classic UI (main.py)
- Traditional Tkinter interface
- System default styling
- Menu-based navigation
- Compatible with all systems

### ✨ Modern UI (modern_main.py) - **NEW!**
- Contemporary CustomTkinter interface
- Dark/Light theme support
- Rounded corners and modern styling
- Professional typography with icons
- Sidebar navigation

## Features

### 🔄 Merge PDFs
- Combine multiple PDF files into a single document
- Drag and drop support for easy file selection
- Reorder files before merging
- Real-time file information display

### 📄 Extract Pages
- Extract specific pages from PDF documents
- Visual page thumbnails for easy selection
- Support for page ranges (e.g., "1-3,5,7-9")
- Select all/none functionality

### 🔀 Sort Pages
- Sort pages manually or automatically
- Options: ascending, descending, reverse order
- Visual page preview
- Drag and drop reordering (planned feature)

### ✂️ Crop Pages
- Crop pages with custom coordinates
- Preset crop options (top/bottom half, left/right half, center)
- Apply to single page, all pages, or page ranges
- Real-time coordinate input

### 📝 Create PDFs
- Generate PDFs from text content
- Create PDFs from image files (PNG, JPEG, GIF, BMP, TIFF)
- Customizable font size for text PDFs
- Batch image processing

### 🎯 Additional Features
- Progress tracking for all operations
- Comprehensive error handling
- File validation
- Status updates
- Cross-platform compatibility (Windows focus)

## Installation

### Prerequisites
- Python 3.7 or higher
- Windows 10/11 (primary target, but works on other platforms)

### Method 1: Using pip (Recommended)

1. **Clone or download the repository:**
   ```bash
   git clone <repository-url>
   cd pdf-manipulator
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```bash
   # Classic UI
   python main.py

   # Modern UI (recommended)
   python modern_main.py

   # Compare both versions
   python demo_comparison.py
   ```

### Method 2: Automated Installation (Windows)

1. **Download the project files**
2. **Run the installer:**
   ```bash
   install.bat
   ```
   This will automatically install dependencies and build the executable.

### Method 3: Manual Dependency Installation

```bash
pip install PyMuPDF==1.23.14
pip install Pillow==10.1.0
pip install tkinterdnd2==0.3.0
pip install customtkinter==5.2.2  # For modern UI
pip install pyinstaller==6.3.0  # Only needed for building executable
```

## Building Executable

To create a standalone Windows executable:

1. **Install PyInstaller:**
   ```bash
   pip install pyinstaller
   ```

2. **Run the build script:**
   ```bash
   python build_exe.py
   ```

3. **Find the executable:**
   The `PDFManipulator.exe` file will be created in the `dist` folder.

### Build Options

The build script supports several customization options:
- Single file executable (no installation required)
- No console window (clean GUI experience)
- Includes all necessary dependencies
- Optimized file size

## Usage Guide

### Getting Started

1. **Launch the application:**
   - Run `python main.py` or double-click `PDFManipulator.exe`

2. **Choose an operation:**
   - Use the tabs at the top to select the desired operation
   - Each tab provides a specific PDF manipulation function

### Merge PDFs

1. **Add files:**
   - Drag and drop PDF files onto the designated area
   - Or click "Add Files" to browse for files
   - Use "Move Up/Down" to reorder files

2. **Select output:**
   - Click "Browse..." to choose output location
   - Enter desired filename

3. **Merge:**
   - Click "Merge PDFs" to start the process
   - Monitor progress in the dialog

### Extract Pages

1. **Select PDF:**
   - Click "Browse..." to select a PDF file
   - Thumbnails will load automatically

2. **Choose pages:**
   - Click checkboxes on thumbnails to select pages
   - Use "Select All/None" for quick selection
   - Enter page ranges in the text field (e.g., "1-3,5,7-9")

3. **Extract:**
   - Choose output location
   - Click "Extract Pages"

### Sort Pages

1. **Load PDF:**
   - Select a PDF file using "Browse..."

2. **Choose sort method:**
   - Manual (drag and drop - planned)
   - Ascending page numbers
   - Descending page numbers
   - Reverse current order

3. **Save:**
   - Select output location
   - Click "Save Sorted PDF"

### Crop Pages

1. **Select PDF:**
   - Choose a PDF file to crop

2. **Set crop area:**
   - Enter coordinates manually (in points)
   - Use preset options for common crops
   - Preview shows selected page

3. **Choose pages:**
   - Current page only
   - All pages
   - Specific page range

4. **Crop:**
   - Select output location
   - Click "Crop Pages"

### Create from Text

1. **Menu → Tools → Create PDF from Text**
2. **Enter text content** in the dialog
3. **Choose font size** (8-72 points)
4. **Select output location**
5. **Click "Create PDF"**

### Create from Images

1. **Menu → Tools → Create PDF from Images**
2. **Select image files** (multiple selection supported)
3. **Choose output location**
4. **Monitor progress** during creation

## File Structure

```
pdf-manipulator/
├── main.py              # Main application entry point
├── pdf_processor.py     # Core PDF processing logic
├── gui_components.py    # Custom GUI components
├── utils.py            # Utility functions
├── requirements.txt    # Python dependencies
├── build_exe.py       # Executable build script
├── install.bat        # Windows installer script
└── README.md          # This file
```

## Dependencies

- **PyMuPDF (fitz)**: PDF processing and manipulation
- **Pillow (PIL)**: Image processing and handling
- **tkinterdnd2**: Drag and drop functionality for Tkinter
- **CustomTkinter**: Modern UI framework (for modern_main.py)
- **PyInstaller**: Creating standalone executables (build only)

## Troubleshooting

### Common Issues

1. **"Module not found" errors:**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`

2. **Drag and drop not working:**
   - Check that tkinterdnd2 is properly installed
   - Try reinstalling: `pip uninstall tkinterdnd2 && pip install tkinterdnd2`

3. **PDF files not opening:**
   - Verify the PDF file is not corrupted
   - Check file permissions
   - Ensure the file is a valid PDF

4. **Executable build fails:**
   - Install PyInstaller: `pip install pyinstaller`
   - Check that all source files are present
   - Run from the project directory

5. **Memory issues with large PDFs:**
   - Close other applications to free memory
   - Process files in smaller batches
   - Use page extraction for large documents

### Performance Tips

- **Large files**: Use page extraction to work with smaller portions
- **Multiple operations**: Close and reopen files between operations
- **Memory usage**: Restart the application periodically for large batches

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. Please check the license file for details.

## Support

For support, please:
1. Check this README for common solutions
2. Search existing issues
3. Create a new issue with detailed information

---

**PDF Manipulator** - Making PDF processing simple and accessible.
