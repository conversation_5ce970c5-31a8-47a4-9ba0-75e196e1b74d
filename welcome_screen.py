"""
Welcome screen for the PDF Manipulator application.
"""

import customtkinter as ctk
import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import io
import base64
from typing import Callable


class WelcomeScreen(ctk.CTkToplevel):
    """Modern welcome screen with logo and start button."""

    def __init__(self, parent, start_callback: Callable = None):
        super().__init__(parent)

        self.start_callback = start_callback

        # Configure window
        self.title("PDF Manipulator Pro")
        self.geometry("800x600")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()

        # Center the window
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 100, parent.winfo_rooty() + 50))

        # Set appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        self.create_interface()

        # Bind close event
        self.protocol("WM_DELETE_WINDOW", self._on_close)

    def create_interface(self):
        """Create the welcome screen interface."""
        # Main container with gradient-like background
        self.main_frame = ctk.CTkFrame(
            self,
            corner_radius=0,
            fg_color=("gray95", "gray10")
        )
        self.main_frame.pack(fill="both", expand=True)

        # Header section
        self.header_frame = ctk.CTkFrame(
            self.main_frame,
            corner_radius=0,
            fg_color="transparent",
            height=200
        )
        self.header_frame.pack(fill="x", pady=(40, 20))
        self.header_frame.pack_propagate(False)

        # Logo
        self.logo_frame = ctk.CTkFrame(
            self.header_frame,
            corner_radius=20,
            width=120,
            height=120,
            fg_color=("white", "gray20"),
            border_width=2,
            border_color=("gray80", "gray60")
        )
        self.logo_frame.pack(pady=(20, 15))
        self.logo_frame.pack_propagate(False)

        # Create logo image
        self.create_logo()

        # App title
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="PDF Manipulator Pro",
            font=ctk.CTkFont(size=32, weight="bold"),
            text_color=("gray20", "white")
        )
        self.title_label.pack(pady=(10, 5))

        # Subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.header_frame,
            text="Advanced PDF Processing Made Simple",
            font=ctk.CTkFont(size=16),
            text_color=("gray50", "gray70")
        )
        self.subtitle_label.pack()

        # Features section
        self.features_frame = ctk.CTkFrame(
            self.main_frame,
            corner_radius=15,
            fg_color=("white", "gray15"),
            border_width=1,
            border_color=("gray90", "gray30")
        )
        self.features_frame.pack(fill="x", padx=60, pady=20)

        # Features title
        features_title = ctk.CTkLabel(
            self.features_frame,
            text="✨ Key Features",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("gray20", "white")
        )
        features_title.pack(pady=(20, 15))

        # Features grid
        self.create_features_grid()

        # Action buttons
        self.buttons_frame = ctk.CTkFrame(
            self.main_frame,
            corner_radius=0,
            fg_color="transparent"
        )
        self.buttons_frame.pack(fill="x", pady=(30, 40))

        # Start button
        self.start_button = ctk.CTkButton(
            self.buttons_frame,
            text="🚀 Start Application",
            command=self._start_app,
            width=200,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=25,
            fg_color=("blue", "blue"),
            hover_color=("darkblue", "lightblue")
        )
        self.start_button.pack(pady=10)

        # Secondary buttons
        buttons_row = ctk.CTkFrame(self.buttons_frame, fg_color="transparent")
        buttons_row.pack(pady=(10, 0))

        self.demo_button = ctk.CTkButton(
            buttons_row,
            text="📖 View Demo",
            command=self._show_demo,
            width=120,
            height=35,
            font=ctk.CTkFont(size=12),
            corner_radius=15,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        self.demo_button.pack(side="left", padx=5)

        self.about_button = ctk.CTkButton(
            buttons_row,
            text="ℹ️ About",
            command=self._show_about,
            width=120,
            height=35,
            font=ctk.CTkFont(size=12),
            corner_radius=15,
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        self.about_button.pack(side="left", padx=5)

        # Footer
        self.footer_label = ctk.CTkLabel(
            self.main_frame,
            text="© 2024 PDF Manipulator Pro - Professional PDF Processing",
            font=ctk.CTkFont(size=10),
            text_color=("gray60", "gray50")
        )
        self.footer_label.pack(side="bottom", pady=(0, 15))

    def create_logo(self):
        """Create a modern logo for the application."""
        try:
            # Try to load the created logo file
            if os.path.exists("logo.png"):
                logo_img = Image.open("logo.png")
                # Resize to fit the frame
                logo_img = logo_img.resize((100, 100), Image.Resampling.LANCZOS)
            else:
                # Fallback: create logo programmatically
                logo_size = (100, 100)
                logo_img = Image.new('RGBA', logo_size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(logo_img)

                # Draw PDF icon-like design
                # Main rectangle (document)
                rect_color = "#1f538d"  # Blue color
                draw.rounded_rectangle([20, 25, 70, 80], radius=5, fill=rect_color)

                # Document lines
                line_color = "white"
                for i, y in enumerate([35, 42, 49, 56, 63]):
                    width = 30 if i % 2 == 0 else 20
                    draw.rectangle([28, y, 28 + width, y + 2], fill=line_color)

                # Corner fold
                fold_points = [(60, 25), (70, 25), (70, 35), (60, 25)]
                draw.polygon(fold_points, fill="#ffffff", outline=rect_color)

                # Add gear/settings icon overlay
                gear_center = (75, 35)
                gear_radius = 10
                draw.ellipse([gear_center[0] - gear_radius, gear_center[1] - gear_radius,
                             gear_center[0] + gear_radius, gear_center[1] + gear_radius],
                            fill="#ff6b35", outline="white", width=2)

        except Exception as e:
            # Ultimate fallback: simple colored rectangle
            logo_img = Image.new('RGBA', (100, 100), "#1f538d")
            draw = ImageDraw.Draw(logo_img)
            draw.text((25, 40), "PDF", fill="white", font_size=20)

        # Convert to CTkImage
        self.logo_image = ctk.CTkImage(
            light_image=logo_img,
            dark_image=logo_img,
            size=(100, 100)
        )

        # Display logo
        self.logo_label = ctk.CTkLabel(
            self.logo_frame,
            image=self.logo_image,
            text=""
        )
        self.logo_label.pack(expand=True)

    def create_features_grid(self):
        """Create a grid of feature highlights."""
        features = [
            ("🔄", "Merge PDFs", "Combine multiple PDF files"),
            ("📄", "Extract Pages", "Select and extract specific pages"),
            ("🔀", "Sort Pages", "Reorder pages automatically"),
            ("✂️", "Crop Pages", "Custom cropping with presets"),
            ("📝", "Create PDFs", "Generate from text or images"),
            ("🎨", "Modern UI", "Dark/Light theme support")
        ]

        # Create grid container
        grid_frame = ctk.CTkFrame(self.features_frame, fg_color="transparent")
        grid_frame.pack(padx=20, pady=(0, 20))

        # Configure grid
        for i in range(3):
            grid_frame.grid_columnconfigure(i, weight=1)

        # Add features
        for i, (icon, title, desc) in enumerate(features):
            row = i // 3
            col = i % 3

            feature_frame = ctk.CTkFrame(
                grid_frame,
                corner_radius=10,
                fg_color=("gray95", "gray20"),
                width=180,
                height=80
            )
            feature_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
            feature_frame.grid_propagate(False)

            # Icon
            icon_label = ctk.CTkLabel(
                feature_frame,
                text=icon,
                font=ctk.CTkFont(size=24)
            )
            icon_label.pack(pady=(8, 2))

            # Title
            title_label = ctk.CTkLabel(
                feature_frame,
                text=title,
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color=("gray20", "white")
            )
            title_label.pack()

            # Description
            desc_label = ctk.CTkLabel(
                feature_frame,
                text=desc,
                font=ctk.CTkFont(size=9),
                text_color=("gray50", "gray70")
            )
            desc_label.pack(pady=(0, 5))

    def _start_app(self):
        """Start the main application."""
        if self.start_callback:
            self.start_callback()
        self.destroy()

    def _show_demo(self):
        """Show demo comparison."""
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "demo_comparison.py"])
        except Exception as e:
            from tkinter import messagebox
            messagebox.showinfo("Demo", "Demo comparison script not found.\nPlease run 'python demo_comparison.py' manually.")

    def _show_about(self):
        """Show about dialog."""
        about_window = ctk.CTkToplevel(self)
        about_window.title("About PDF Manipulator Pro")
        about_window.geometry("500x400")
        about_window.transient(self)
        about_window.grab_set()

        # Center the about window
        about_window.geometry("+%d+%d" % (self.winfo_rootx() + 150, self.winfo_rooty() + 100))

        # About content
        about_frame = ctk.CTkFrame(about_window, corner_radius=15)
        about_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            about_frame,
            text="PDF Manipulator Pro",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title.pack(pady=(20, 10))

        # Version
        version = ctk.CTkLabel(
            about_frame,
            text="Version 2.0 - Modern UI Edition",
            font=ctk.CTkFont(size=14),
            text_color=("gray60", "gray40")
        )
        version.pack(pady=(0, 20))

        # Description
        description = ctk.CTkTextbox(
            about_frame,
            corner_radius=10,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        description.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        about_text = """A comprehensive PDF manipulation application with modern UI/UX design.

Features:
• Merge multiple PDF files with drag & drop support
• Extract specific pages with visual thumbnails
• Sort pages manually or automatically
• Crop pages with custom coordinates or presets
• Create PDFs from text content or images
• Modern dark/light theme interface
• Progress tracking for all operations
• Professional styling with CustomTkinter

Built with:
• Python 3.7+
• CustomTkinter for modern UI
• PyMuPDF (fitz) for PDF processing
• Pillow for image handling
• tkinterdnd2 for drag & drop

© 2024 PDF Manipulator Pro
Professional PDF processing made simple."""

        description.insert("1.0", about_text)
        description.configure(state="disabled")

        # Close button
        close_btn = ctk.CTkButton(
            about_frame,
            text="Close",
            command=about_window.destroy,
            width=100,
            height=35,
            corner_radius=10
        )
        close_btn.pack(pady=(0, 20))

    def _on_close(self):
        """Handle window close event."""
        self.destroy()


def show_welcome_screen(parent, start_callback=None):
    """Show the welcome screen."""
    welcome = WelcomeScreen(parent, start_callback)
    return welcome
