"""
Simplified modern app without drag-and-drop to test basic functionality.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import os


class SimpleModernApp:
    """Simplified modern PDF application."""
    
    def __init__(self):
        # Set CustomTkinter appearance
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("PDF Manipulator Pro - Simple Modern")
        self.root.geometry("800x600")
        
        # Center window
        self.center_window()
        
        # Set icon if available
        self.set_icon()
        
        # Create interface
        self.create_interface()
        
        print("Simple modern app created successfully")
    
    def center_window(self):
        """Center the window on screen."""
        self.root.update_idletasks()
        width = 800
        height = 600
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def set_icon(self):
        """Set window icon if available."""
        try:
            if os.path.exists("icon.ico"):
                self.root.iconbitmap("icon.ico")
        except Exception:
            pass
    
    def create_interface(self):
        """Create the modern interface."""
        # Main container
        main_container = ctk.CTkFrame(self.root, corner_radius=0)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_container,
            text="PDF Manipulator Pro",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            main_container,
            text="Modern PDF Processing Interface",
            font=ctk.CTkFont(size=16),
            text_color=("gray60", "gray40")
        )
        subtitle_label.pack(pady=(0, 30))
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Modern interface is working!")
        status_label = ctk.CTkLabel(
            main_container,
            textvariable=self.status_var,
            font=ctk.CTkFont(size=14),
            text_color=("green", "lightgreen")
        )
        status_label.pack(pady=10)
        
        # Test buttons frame
        buttons_frame = ctk.CTkFrame(main_container, corner_radius=15)
        buttons_frame.pack(fill="x", pady=20, padx=20)
        
        buttons_title = ctk.CTkLabel(
            buttons_frame,
            text="🧪 Test Functions",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        buttons_title.pack(pady=(20, 15))
        
        # Test CustomTkinter
        test_ctk_button = ctk.CTkButton(
            buttons_frame,
            text="Test CustomTkinter",
            command=self.test_customtkinter,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        test_ctk_button.pack(pady=5, padx=20, fill="x")
        
        # Test file dialog
        test_file_button = ctk.CTkButton(
            buttons_frame,
            text="Test File Dialog",
            command=self.test_file_dialog,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("gray70", "gray30"),
            hover_color=("gray60", "gray40")
        )
        test_file_button.pack(pady=5, padx=20, fill="x")
        
        # Test dependencies
        test_deps_button = ctk.CTkButton(
            buttons_frame,
            text="Test Dependencies",
            command=self.test_dependencies,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color=("orange", "darkorange"),
            hover_color=("darkorange", "orange")
        )
        test_deps_button.pack(pady=5, padx=20, fill="x")
        
        # Launch full app
        launch_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 Try Full Modern App",
            command=self.launch_full_app,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            fg_color=("#1f538d", "#1f538d"),
            hover_color=("#164a7b", "#164a7b")
        )
        launch_button.pack(pady=15, padx=20, fill="x")
        
        # Close button
        close_button = ctk.CTkButton(
            buttons_frame,
            text="Close",
            command=self.root.destroy,
            height=35,
            font=ctk.CTkFont(size=12),
            fg_color=("red", "darkred"),
            hover_color=("darkred", "red")
        )
        close_button.pack(pady=(5, 20), padx=20)
        
        # Info section
        info_frame = ctk.CTkFrame(main_container, corner_radius=15)
        info_frame.pack(fill="both", expand=True, pady=(0, 20), padx=20)
        
        info_title = ctk.CTkLabel(
            info_frame,
            text="ℹ️ Information",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        info_title.pack(pady=(15, 10))
        
        info_text = ctk.CTkTextbox(
            info_frame,
            corner_radius=10,
            font=ctk.CTkFont(size=12)
        )
        info_text.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        info_content = """This is a simplified modern interface test for PDF Manipulator Pro.

If you can see this CustomTkinter interface with modern styling, then the basic modern UI components are working correctly.

Test the buttons above to verify different aspects:

🧪 Test CustomTkinter - Verifies modern UI components work
📁 Test File Dialog - Checks file selection functionality  
🔍 Test Dependencies - Verifies required modules are available
🚀 Try Full Modern App - Attempts to launch the complete application

The modern interface uses:
• CustomTkinter for contemporary styling
• Dark/light theme support
• Rounded corners and modern buttons
• Professional color scheme

If this interface works but the full app doesn't, there may be issues with:
• Drag & drop functionality (tkinterdnd2)
• PDF processing components
• Complex widget interactions"""
        
        info_text.insert("0.0", info_content)
        info_text.configure(state="disabled")
    
    def test_customtkinter(self):
        """Test CustomTkinter functionality."""
        try:
            self.status_var.set("Testing CustomTkinter...")
            self.root.update()
            
            # Create a test dialog
            dialog = ctk.CTkToplevel(self.root)
            dialog.title("CustomTkinter Test")
            dialog.geometry("400x300")
            dialog.transient(self.root)
            dialog.grab_set()
            
            # Center dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (dialog.winfo_screenheight() // 2) - (300 // 2)
            dialog.geometry(f"400x300+{x}+{y}")
            
            # Dialog content
            ctk.CTkLabel(
                dialog,
                text="✅ CustomTkinter Test",
                font=ctk.CTkFont(size=20, weight="bold")
            ).pack(pady=30)
            
            ctk.CTkLabel(
                dialog,
                text="If you can see this modern dialog,\nCustomTkinter is working correctly!",
                font=ctk.CTkFont(size=14)
            ).pack(pady=20)
            
            def close_dialog():
                dialog.destroy()
                self.status_var.set("✅ CustomTkinter test passed!")
            
            ctk.CTkButton(
                dialog,
                text="Close",
                command=close_dialog,
                height=40
            ).pack(pady=30)
            
        except Exception as e:
            self.status_var.set("❌ CustomTkinter test failed!")
            messagebox.showerror("Error", f"CustomTkinter test failed: {str(e)}")
    
    def test_file_dialog(self):
        """Test file dialog functionality."""
        try:
            self.status_var.set("Testing file dialog...")
            self.root.update()
            
            file_path = filedialog.askopenfilename(
                title="Select any file (this is just a test)",
                filetypes=[("All files", "*.*")]
            )
            
            if file_path:
                self.status_var.set("✅ File dialog works!")
                messagebox.showinfo("Test Result", f"✅ File dialog works!\nSelected: {os.path.basename(file_path)}")
            else:
                self.status_var.set("File dialog cancelled")
                
        except Exception as e:
            self.status_var.set("❌ File dialog failed!")
            messagebox.showerror("Error", f"File dialog test failed: {str(e)}")
    
    def test_dependencies(self):
        """Test if required dependencies are available."""
        try:
            self.status_var.set("Testing dependencies...")
            self.root.update()
            
            results = []
            
            # Test each dependency
            try:
                import customtkinter
                results.append("✅ CustomTkinter - Working")
            except ImportError:
                results.append("❌ CustomTkinter - MISSING")
            
            try:
                import fitz
                results.append("✅ PyMuPDF (fitz) - Available")
            except ImportError:
                results.append("❌ PyMuPDF (fitz) - MISSING")
            
            try:
                from PIL import Image
                results.append("✅ Pillow (PIL) - Available")
            except ImportError:
                results.append("❌ Pillow (PIL) - MISSING")
            
            try:
                import tkinterdnd2
                results.append("✅ tkinterdnd2 - Available")
            except ImportError:
                results.append("❌ tkinterdnd2 - MISSING (drag & drop won't work)")
            
            # Show results
            result_text = "Dependency Test Results:\n\n" + "\n".join(results)
            
            if "❌" in result_text:
                self.status_var.set("❌ Some dependencies missing!")
            else:
                self.status_var.set("✅ All dependencies available!")
            
            messagebox.showinfo("Dependency Test", result_text)
                
        except Exception as e:
            self.status_var.set("❌ Dependency test failed!")
            messagebox.showerror("Error", f"Dependency test failed: {str(e)}")
    
    def launch_full_app(self):
        """Try to launch the full modern application."""
        try:
            self.status_var.set("Launching full application...")
            self.root.update()
            
            if os.path.exists("modern_main.py"):
                import subprocess
                import sys
                
                subprocess.Popen([sys.executable, "modern_main.py"])
                self.status_var.set("✅ Full app launched!")
                messagebox.showinfo("Launch", "✅ Full application launched successfully!")
                
            else:
                self.status_var.set("❌ modern_main.py not found!")
                messagebox.showerror("Error", "modern_main.py file not found!")
                
        except Exception as e:
            self.status_var.set("❌ Failed to launch full app!")
            messagebox.showerror("Error", f"Failed to launch full app: {str(e)}")
    
    def run(self):
        """Start the application."""
        print("Starting simple modern app...")
        self.root.mainloop()
        print("Simple modern app closed")


def main():
    """Main entry point."""
    print("PDF Manipulator Pro - Simple Modern Test")
    print("=" * 50)
    
    try:
        app = SimpleModernApp()
        app.run()
    except Exception as e:
        print(f"Error starting simple modern app: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
