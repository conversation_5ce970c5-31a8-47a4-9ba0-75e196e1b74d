"""
Demo script to showcase both Classic and Modern UI versions side by side.
"""

import tkinter as tk
from tkinter import ttk
import subprocess
import sys
import os
from pathlib import Path


class UIComparisonDemo:
    """Demo application to compare Classic vs Modern UI."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PDF Manipulator - UI Comparison Demo")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
        
        self.create_interface()
    
    def create_interface(self):
        """Create the demo interface."""
        # Title
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(fill='x', pady=20)
        
        title_label = tk.Label(
            title_frame,
            text="PDF Manipulator - UI Comparison",
            font=('Arial', 24, 'bold'),
            bg='#f0f0f0',
            fg='#333333'
        )
        title_label.pack()
        
        subtitle_label = tk.Label(
            title_frame,
            text="Experience the difference between Classic and Modern interfaces",
            font=('Arial', 12),
            bg='#f0f0f0',
            fg='#666666'
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Main content
        content_frame = tk.Frame(self.root, bg='#f0f0f0')
        content_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        # Classic UI Section
        classic_frame = tk.LabelFrame(
            content_frame,
            text="Classic UI",
            font=('Arial', 14, 'bold'),
            bg='#f0f0f0',
            fg='#333333',
            padx=20,
            pady=20
        )
        classic_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        classic_desc = tk.Label(
            classic_frame,
            text="• Traditional Tkinter interface\n• System default colors and fonts\n• Standard rectangular buttons\n• Basic layout and styling\n• Menu-based navigation",
            font=('Arial', 11),
            bg='#f0f0f0',
            fg='#555555',
            justify='left'
        )
        classic_desc.pack(anchor='w', pady=(0, 15))
        
        classic_button = tk.Button(
            classic_frame,
            text="Launch Classic UI",
            command=self.launch_classic,
            font=('Arial', 12, 'bold'),
            bg='#4CAF50',
            fg='white',
            relief='raised',
            bd=2,
            padx=20,
            pady=10
        )
        classic_button.pack()
        
        # Modern UI Section
        modern_frame = tk.LabelFrame(
            content_frame,
            text="Modern UI",
            font=('Arial', 14, 'bold'),
            bg='#f0f0f0',
            fg='#333333',
            padx=20,
            pady=20
        )
        modern_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        modern_desc = tk.Label(
            modern_frame,
            text="• CustomTkinter modern interface\n• Dark/Light theme support\n• Rounded corners and modern styling\n• Professional typography and icons\n• Sidebar navigation with quick actions",
            font=('Arial', 11),
            bg='#f0f0f0',
            fg='#555555',
            justify='left'
        )
        modern_desc.pack(anchor='w', pady=(0, 15))
        
        modern_button = tk.Button(
            modern_frame,
            text="Launch Modern UI",
            command=self.launch_modern,
            font=('Arial', 12, 'bold'),
            bg='#2196F3',
            fg='white',
            relief='raised',
            bd=2,
            padx=20,
            pady=10
        )
        modern_button.pack()
        
        # Footer
        footer_frame = tk.Frame(self.root, bg='#f0f0f0')
        footer_frame.pack(fill='x', pady=20)
        
        footer_label = tk.Label(
            footer_frame,
            text="Both versions provide the same powerful PDF processing capabilities",
            font=('Arial', 10, 'italic'),
            bg='#f0f0f0',
            fg='#888888'
        )
        footer_label.pack()
        
        # Status
        self.status_var = tk.StringVar(value="Ready - Choose a version to launch")
        status_label = tk.Label(
            footer_frame,
            textvariable=self.status_var,
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#333333'
        )
        status_label.pack(pady=(10, 0))
    
    def launch_classic(self):
        """Launch the classic UI version."""
        self.status_var.set("Launching Classic UI...")
        self.root.update()
        
        try:
            if Path("main.py").exists():
                subprocess.Popen([sys.executable, "main.py"])
                self.status_var.set("Classic UI launched successfully!")
            else:
                self.status_var.set("Error: main.py not found")
        except Exception as e:
            self.status_var.set(f"Error launching Classic UI: {str(e)}")
    
    def launch_modern(self):
        """Launch the modern UI version."""
        self.status_var.set("Launching Modern UI...")
        self.root.update()
        
        try:
            if Path("modern_main.py").exists():
                subprocess.Popen([sys.executable, "modern_main.py"])
                self.status_var.set("Modern UI launched successfully!")
            else:
                self.status_var.set("Error: modern_main.py not found")
        except Exception as e:
            self.status_var.set(f"Error launching Modern UI: {str(e)}")
    
    def run(self):
        """Start the demo application."""
        self.root.mainloop()


def main():
    """Main entry point."""
    print("PDF Manipulator UI Comparison Demo")
    print("==================================")
    
    # Check if both versions exist
    classic_exists = Path("main.py").exists()
    modern_exists = Path("modern_main.py").exists()
    
    if not classic_exists and not modern_exists:
        print("Error: Neither main.py nor modern_main.py found.")
        print("Please run this script from the project directory.")
        return
    
    if not classic_exists:
        print("Warning: main.py not found. Classic UI demo will not work.")
    
    if not modern_exists:
        print("Warning: modern_main.py not found. Modern UI demo will not work.")
    
    # Check dependencies
    try:
        import customtkinter
        print("✓ CustomTkinter found - Modern UI available")
    except ImportError:
        print("⚠ CustomTkinter not found - Install with: pip install customtkinter")
        print("  Modern UI demo may not work without this dependency")
    
    try:
        import tkinterdnd2
        print("✓ tkinterdnd2 found - Drag & drop available")
    except ImportError:
        print("⚠ tkinterdnd2 not found - Install with: pip install tkinterdnd2")
        print("  Drag & drop features may not work without this dependency")
    
    print("\nStarting comparison demo...")
    
    try:
        demo = UIComparisonDemo()
        demo.run()
    except Exception as e:
        print(f"Error starting demo: {e}")


if __name__ == "__main__":
    main()
