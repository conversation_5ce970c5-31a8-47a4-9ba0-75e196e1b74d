#!/usr/bin/env python3
"""
PDF Manipulator Pro - Final Release
A comprehensive PDF manipulation application with modern interface.

Author: PDF Manipulator Pro Team
Version: 2.0
Date: 2024
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required dependencies are installed."""
    missing_deps = []
    
    try:
        import PyPDF2
    except ImportError:
        missing_deps.append("PyPDF2")
    
    try:
        import fitz  # PyMuPDF
    except ImportError:
        missing_deps.append("PyMuPDF")
    
    try:
        from PIL import Image, ImageTk
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import tkinterdnd2
    except ImportError:
        missing_deps.append("tkinterdnd2")
    
    return missing_deps

def show_dependency_error(missing_deps):
    """Show error dialog for missing dependencies."""
    root = tk.Tk()
    root.withdraw()
    
    deps_text = "\n".join(f"• {dep}" for dep in missing_deps)
    message = f"""Missing Required Dependencies:

{deps_text}

Please install the missing packages using:
pip install {' '.join(missing_deps)}

Or install all dependencies at once:
pip install -r requirements.txt"""
    
    messagebox.showerror("Missing Dependencies", message)
    root.destroy()

def main():
    """Main entry point for PDF Manipulator Pro."""
    print("PDF Manipulator Pro v2.0 - Starting...")
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"Error: Missing dependencies: {', '.join(missing_deps)}")
        show_dependency_error(missing_deps)
        return 1
    
    try:
        # Import and start the main application
        from modern_main import ModernPDFManipulatorApp
        
        print("All dependencies found. Launching application...")
        app = ModernPDFManipulatorApp(show_welcome=True)
        app.run()
        
        print("Application closed successfully.")
        return 0
        
    except Exception as e:
        print(f"Error starting application: {e}")
        
        # Show error dialog
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("Application Error", 
                           f"Failed to start PDF Manipulator Pro:\n\n{str(e)}\n\n"
                           "Please check that all files are present and try again.")
        root.destroy()
        return 1

if __name__ == "__main__":
    sys.exit(main())
