"""
Force visible version of the modern app to debug visibility issues.
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import os


def main():
    """Create a simple but visible modern app."""
    print("Creating force visible app...")
    
    # Set CustomTkinter appearance
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    
    # Create main window
    root = ctk.CTk()
    root.title("PDF Manipulator Pro - Force Visible Test")
    root.geometry("600x400")
    
    print("Window created, setting properties...")
    
    # Force window to be visible and on top
    root.lift()
    root.attributes('-topmost', True)
    root.update()
    root.attributes('-topmost', False)
    root.focus_force()
    
    # Center window
    root.update_idletasks()
    width = 600
    height = 400
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    print(f"Window positioned at {x}, {y}")
    
    # Set icon if available
    try:
        if os.path.exists("icon.ico"):
            root.iconbitmap("icon.ico")
            print("Icon set successfully")
    except Exception as e:
        print(f"Could not set icon: {e}")
    
    # Create simple content
    main_frame = ctk.CTkFrame(root, corner_radius=15)
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # Title
    title_label = ctk.CTkLabel(
        main_frame,
        text="PDF Manipulator Pro",
        font=ctk.CTkFont(size=24, weight="bold")
    )
    title_label.pack(pady=30)
    
    # Status
    status_label = ctk.CTkLabel(
        main_frame,
        text="✅ Application is visible and working!",
        font=ctk.CTkFont(size=16),
        text_color=("green", "lightgreen")
    )
    status_label.pack(pady=20)
    
    # Test button
    def test_click():
        messagebox.showinfo("Test", "✅ Button click works!\nThe application is responding correctly.")
    
    test_button = ctk.CTkButton(
        main_frame,
        text="🧪 Test Button",
        command=test_click,
        height=50,
        font=ctk.CTkFont(size=16, weight="bold")
    )
    test_button.pack(pady=20)
    
    # Launch full app button
    def launch_full():
        try:
            import subprocess
            import sys
            subprocess.Popen([sys.executable, "modern_main.py"])
            messagebox.showinfo("Launch", "Full app launched in separate process")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch full app: {e}")
    
    launch_button = ctk.CTkButton(
        main_frame,
        text="🚀 Launch Full App",
        command=launch_full,
        height=40,
        font=ctk.CTkFont(size=14, weight="bold"),
        fg_color=("#1f538d", "#1f538d")
    )
    launch_button.pack(pady=10)
    
    # Close button
    close_button = ctk.CTkButton(
        main_frame,
        text="Close",
        command=root.destroy,
        height=35,
        fg_color=("red", "darkred")
    )
    close_button.pack(pady=20)
    
    # Info
    info_label = ctk.CTkLabel(
        main_frame,
        text="If you can see this window, CustomTkinter is working correctly.\nThe issue with the main app might be elsewhere.",
        font=ctk.CTkFont(size=12),
        text_color=("gray60", "gray40")
    )
    info_label.pack(pady=10)
    
    print("Interface created, starting mainloop...")
    
    # Make sure window is really visible
    root.deiconify()
    root.update()
    
    # Start the main loop
    try:
        root.mainloop()
        print("App closed normally")
    except Exception as e:
        print(f"Error in mainloop: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
