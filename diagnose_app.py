"""
Diagnostic script to identify why the app is not responding.
"""

import sys
import os
import traceback


def check_dependencies():
    """Check if all required dependencies are installed."""
    print("Checking dependencies...")
    
    required_modules = [
        'tkinter',
        'customtkinter', 
        'PIL',
        'fitz',
        'tkinterdnd2'
    ]
    
    missing = []
    
    for module in required_modules:
        try:
            if module == 'fitz':
                import fitz
                print(f"✓ PyMuPDF (fitz) - version {fitz.version[0]}")
            elif module == 'PIL':
                from PIL import Image
                print(f"✓ Pillow (PIL) - available")
            elif module == 'tkinter':
                import tkinter as tk
                print(f"✓ tkinter - available")
            elif module == 'customtkinter':
                import customtkinter as ctk
                print(f"✓ customtkinter - available")
            elif module == 'tkinterdnd2':
                import tkinterdnd2
                print(f"✓ tkinterdnd2 - available")
        except ImportError as e:
            print(f"✗ {module} - MISSING")
            missing.append(module)
    
    return missing


def check_files():
    """Check if all required files exist."""
    print("\nChecking required files...")
    
    required_files = [
        'main.py',
        'modern_main.py',
        'pdf_processor.py',
        'utils.py',
        'gui_components.py',
        'modern_gui_components.py',
        'simple_welcome.py'
    ]
    
    missing = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - MISSING")
            missing.append(file)
    
    return missing


def test_basic_tkinter():
    """Test if basic tkinter works."""
    print("\nTesting basic tkinter...")
    
    try:
        import tkinter as tk
        
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")
        
        label = tk.Label(root, text="If you see this, tkinter works!")
        label.pack(pady=50)
        
        close_button = tk.Button(root, text="Close", command=root.destroy)
        close_button.pack()
        
        print("✓ Basic tkinter window created")
        print("  A test window should appear - close it to continue")
        
        root.mainloop()
        print("✓ Basic tkinter test completed")
        return True
        
    except Exception as e:
        print(f"✗ Basic tkinter test failed: {e}")
        return False


def test_customtkinter():
    """Test if customtkinter works."""
    print("\nTesting customtkinter...")
    
    try:
        import customtkinter as ctk
        
        ctk.set_appearance_mode("dark")
        
        root = ctk.CTk()
        root.title("CustomTkinter Test")
        root.geometry("300x200")
        
        label = ctk.CTkLabel(root, text="CustomTkinter works!")
        label.pack(pady=30)
        
        button = ctk.CTkButton(root, text="Close", command=root.destroy)
        button.pack()
        
        print("✓ CustomTkinter window created")
        print("  A modern test window should appear - close it to continue")
        
        root.mainloop()
        print("✓ CustomTkinter test completed")
        return True
        
    except Exception as e:
        print(f"✗ CustomTkinter test failed: {e}")
        traceback.print_exc()
        return False


def test_pdf_processor():
    """Test if PDF processor can be imported."""
    print("\nTesting PDF processor...")
    
    try:
        from pdf_processor import PDFProcessor
        processor = PDFProcessor()
        print("✓ PDF processor imported successfully")
        return True
    except Exception as e:
        print(f"✗ PDF processor test failed: {e}")
        traceback.print_exc()
        return False


def test_simple_welcome():
    """Test if simple welcome screen works."""
    print("\nTesting simple welcome screen...")
    
    try:
        import tkinter as tk
        from simple_welcome import show_simple_welcome
        
        root = tk.Tk()
        root.withdraw()  # Hide root
        
        def test_callback():
            print("✓ Welcome screen callback works")
            root.quit()
        
        welcome = show_simple_welcome(root, test_callback)
        print("✓ Simple welcome screen created")
        print("  A welcome screen should appear - click 'Start Application' to continue")
        
        root.mainloop()
        print("✓ Simple welcome screen test completed")
        return True
        
    except Exception as e:
        print(f"✗ Simple welcome screen test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all diagnostic tests."""
    print("PDF Manipulator Pro - Diagnostic Tool")
    print("=" * 50)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    # Check files
    missing_files = check_files()
    
    # Summary
    print("\n" + "=" * 50)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 50)
    
    if missing_deps:
        print(f"❌ Missing dependencies: {', '.join(missing_deps)}")
        print("   Install with: pip install " + " ".join(missing_deps))
        return
    else:
        print("✅ All dependencies are installed")
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        print("   Please ensure all application files are present")
        return
    else:
        print("✅ All required files are present")
    
    # Interactive tests
    print("\n" + "=" * 50)
    print("INTERACTIVE TESTS")
    print("=" * 50)
    
    response = input("\nRun basic tkinter test? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        if not test_basic_tkinter():
            print("❌ Basic tkinter failed - GUI applications won't work")
            return
    
    response = input("\nRun CustomTkinter test? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        if not test_customtkinter():
            print("❌ CustomTkinter failed - modern UI won't work")
            print("   Try the classic UI instead: python main.py")
            return
    
    response = input("\nTest PDF processor? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        if not test_pdf_processor():
            print("❌ PDF processor failed - core functionality won't work")
            return
    
    response = input("\nTest welcome screen? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        if not test_simple_welcome():
            print("❌ Welcome screen failed")
            return
    
    print("\n" + "=" * 50)
    print("✅ ALL TESTS PASSED!")
    print("=" * 50)
    print("The application should work. Try running:")
    print("  python modern_main.py")
    print("  or")
    print("  python launch_app.py")


if __name__ == "__main__":
    main()
