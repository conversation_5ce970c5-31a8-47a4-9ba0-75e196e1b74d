"""
Test script to verify core PDF processing functionality.
"""

import os
import tempfile
from pdf_processor import PDFProcessor
from utils import validate_pdf_file, get_pdf_info


def test_create_pdf_from_text():
    """Test creating PDF from text."""
    print("Testing PDF creation from text...")
    
    processor = PDFProcessor()
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        output_path = tmp_file.name
    
    try:
        # Test text content
        test_text = """This is a test PDF document created by the PDF Manipulator application.

This application provides comprehensive PDF processing capabilities including:
- Merging multiple PDF files
- Extracting specific pages
- Sorting pages in different orders
- Cropping pages with custom coordinates
- Creating PDFs from text or images

The application is built with Python, Tkinter, and PyMuPDF (fitz) for robust PDF processing."""
        
        # Create PDF
        success = processor.create_pdf_from_text(test_text, output_path, font_size=12)
        
        if success:
            print("✓ PDF created successfully")
            
            # Validate the created PDF
            if validate_pdf_file(output_path):
                print("✓ Created PDF is valid")
                
                # Get PDF info
                info = get_pdf_info(output_path)
                if info:
                    print(f"✓ PDF has {info['page_count']} page(s)")
                    print(f"✓ File size: {info['file_size']} bytes")
                else:
                    print("✗ Could not get PDF info")
            else:
                print("✗ Created PDF is not valid")
        else:
            print("✗ Failed to create PDF")
    
    finally:
        # Clean up
        if os.path.exists(output_path):
            os.unlink(output_path)
    
    processor.close_current_pdf()


def test_pdf_info_functions():
    """Test PDF information functions."""
    print("\nTesting PDF info functions...")
    
    # Create a test PDF first
    processor = PDFProcessor()
    
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        test_pdf_path = tmp_file.name
    
    try:
        # Create a test PDF
        success = processor.create_pdf_from_text("Test PDF for info extraction", test_pdf_path)
        
        if success:
            print("✓ Test PDF created")
            
            # Test validation
            if validate_pdf_file(test_pdf_path):
                print("✓ PDF validation works")
            else:
                print("✗ PDF validation failed")
            
            # Test info extraction
            info = get_pdf_info(test_pdf_path)
            if info:
                print("✓ PDF info extraction works")
                print(f"  - Pages: {info['page_count']}")
                print(f"  - Size: {info['file_size']} bytes")
            else:
                print("✗ PDF info extraction failed")
        else:
            print("✗ Failed to create test PDF")
    
    finally:
        if os.path.exists(test_pdf_path):
            os.unlink(test_pdf_path)
    
    processor.close_current_pdf()


def test_pdf_loading():
    """Test PDF loading functionality."""
    print("\nTesting PDF loading...")
    
    processor = PDFProcessor()
    
    # Create a test PDF
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        test_pdf_path = tmp_file.name
    
    try:
        # Create test PDF
        success = processor.create_pdf_from_text("Test PDF for loading\n\nPage 1 content", test_pdf_path)
        
        if success:
            print("✓ Test PDF created")
            
            # Test loading
            if processor.load_pdf(test_pdf_path):
                print("✓ PDF loaded successfully")
                
                # Test page count
                page_count = processor.get_page_count()
                print(f"✓ Page count: {page_count}")
                
                # Test thumbnail generation
                if page_count > 0:
                    thumbnail = processor.get_page_thumbnail(0, 0.5)
                    if thumbnail:
                        print("✓ Thumbnail generation works")
                        print(f"  - Thumbnail size: {thumbnail.size}")
                    else:
                        print("✗ Thumbnail generation failed")
            else:
                print("✗ Failed to load PDF")
        else:
            print("✗ Failed to create test PDF")
    
    finally:
        if os.path.exists(test_pdf_path):
            os.unlink(test_pdf_path)
    
    processor.close_current_pdf()


def test_page_extraction():
    """Test page extraction functionality."""
    print("\nTesting page extraction...")
    
    processor = PDFProcessor()
    
    # Create a multi-page test PDF
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        source_pdf_path = tmp_file.name
    
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
        extracted_pdf_path = tmp_file.name
    
    try:
        # Create a multi-page PDF by creating multiple single-page PDFs and merging
        # For simplicity, we'll create a single page and extract it
        success = processor.create_pdf_from_text("Test page for extraction", source_pdf_path)
        
        if success:
            print("✓ Source PDF created")
            
            # Load the PDF
            if processor.load_pdf(source_pdf_path):
                print("✓ Source PDF loaded")
                
                # Extract page 0
                success = processor.extract_pages([0], extracted_pdf_path)
                
                if success:
                    print("✓ Page extraction successful")
                    
                    # Validate extracted PDF
                    if validate_pdf_file(extracted_pdf_path):
                        print("✓ Extracted PDF is valid")
                        
                        info = get_pdf_info(extracted_pdf_path)
                        if info:
                            print(f"✓ Extracted PDF has {info['page_count']} page(s)")
                    else:
                        print("✗ Extracted PDF is not valid")
                else:
                    print("✗ Page extraction failed")
            else:
                print("✗ Failed to load source PDF")
        else:
            print("✗ Failed to create source PDF")
    
    finally:
        for path in [source_pdf_path, extracted_pdf_path]:
            if os.path.exists(path):
                os.unlink(path)
    
    processor.close_current_pdf()


def main():
    """Run all tests."""
    print("PDF Manipulator Functionality Tests")
    print("=" * 40)
    
    try:
        test_create_pdf_from_text()
        test_pdf_info_functions()
        test_pdf_loading()
        test_page_extraction()
        
        print("\n" + "=" * 40)
        print("All tests completed!")
        print("The core PDF processing functionality is working correctly.")
        
    except Exception as e:
        print(f"\nError during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
