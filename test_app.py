#!/usr/bin/env python3
"""
Test script for PDF Manipulator Pro
"""

import os
import sys
import time
import threading
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    try:
        import tkinter as tk
        from tkinter import ttk, filedialog, messagebox
        import PyPDF2
        from PIL import Image, ImageTk
        import fitz  # PyMuPDF
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_app_creation():
    """Test that the main app can be created."""
    print("Testing app creation...")
    try:
        from modern_main import ModernPDFManipulatorApp

        # Create app without showing welcome screen
        app = ModernPDFManipulatorApp(show_welcome=False)
        print("✓ App created successfully")

        # Test that main window exists
        if hasattr(app, 'root') and app.root:
            print("✓ Main window created")

            # Close the app after a short delay
            def close_app():
                time.sleep(2)
                try:
                    app.root.quit()
                    app.root.destroy()
                except:
                    pass

            threading.Thread(target=close_app, daemon=True).start()
            app.root.mainloop()

            return True
        else:
            print("✗ Main window not created")
            return False

    except Exception as e:
        print(f"✗ App creation error: {e}")
        return False

def test_welcome_screen():
    """Test the welcome screen."""
    print("Testing welcome screen...")
    try:
        from simple_welcome import SimpleWelcomeScreen
        import tkinter as tk

        def dummy_callback():
            print("Welcome screen callback called")

        # Create a root window first
        root = tk.Tk()
        root.withdraw()  # Hide the root window

        # Create welcome screen
        welcome = SimpleWelcomeScreen(root, dummy_callback)
        print("✓ Welcome screen created successfully")

        # Close after short delay
        def close_welcome():
            time.sleep(2)
            try:
                welcome.window.quit()
                welcome.window.destroy()
                root.quit()
                root.destroy()
            except:
                pass

        threading.Thread(target=close_welcome, daemon=True).start()
        root.mainloop()

        return True

    except Exception as e:
        print(f"✗ Welcome screen error: {e}")
        return False

def main():
    """Run all tests."""
    print("PDF Manipulator Pro - Test Suite")
    print("=" * 40)

    tests = [
        test_imports,
        test_app_creation,
        test_welcome_screen
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            print()

    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
