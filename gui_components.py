"""
Custom GUI components for the PDF manipulation application.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
from tkinterdnd2 import DND_FILES, TkinterDnD
from typing import List, Callable, Optional
from PIL import Image, ImageTk
import threading


class DragDropFrame(tk.Frame):
    """Frame that supports drag and drop for PDF files."""
    
    def __init__(self, parent, drop_callback: Callable[[List[str]], None], **kwargs):
        super().__init__(parent, **kwargs)
        self.drop_callback = drop_callback
        
        # Configure drag and drop
        self.drop_target_register(DND_FILES)
        self.dnd_bind('<<Drop>>', self._on_drop)
        
        # Visual styling
        self.configure(relief="sunken", bd=2, bg="#f0f0f0")
        
        # Add label
        self.label = tk.Label(self, text="Drag and drop PDF files here\nor click to browse",
                             bg="#f0f0f0", fg="#666666", font=("Arial", 12))
        self.label.pack(expand=True, fill="both", padx=20, pady=20)
        
        # Bind click event
        self.bind("<Button-1>", self._on_click)
        self.label.bind("<Button-1>", self._on_click)
    
    def _on_drop(self, event):
        """Handle file drop event."""
        files = self.tk.splitlist(event.data)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        if pdf_files:
            self.drop_callback(pdf_files)
    
    def _on_click(self, event):
        """Handle click event to browse files."""
        from utils import select_input_files
        files = select_input_files()
        if files:
            self.drop_callback(files)


class ProgressDialog(tk.Toplevel):
    """Dialog window showing progress of operations."""
    
    def __init__(self, parent, title: str = "Processing..."):
        super().__init__(parent)
        self.title(title)
        self.geometry("400x150")
        self.resizable(False, False)
        self.transient(parent)
        self.grab_set()
        
        # Center the dialog
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(pady=20, padx=20, fill="x")
        
        # Status label
        self.status_var = tk.StringVar(value="Initializing...")
        self.status_label = tk.Label(self, textvariable=self.status_var)
        self.status_label.pack(pady=10)
        
        # Cancel button
        self.cancel_button = ttk.Button(self, text="Cancel", command=self._on_cancel)
        self.cancel_button.pack(pady=10)
        
        self.cancelled = False
    
    def update_progress(self, progress: float, status: str = ""):
        """Update progress bar and status."""
        self.progress_var.set(progress)
        if status:
            self.status_var.set(status)
        self.update()
    
    def _on_cancel(self):
        """Handle cancel button click."""
        self.cancelled = True
        self.destroy()


class PageThumbnailFrame(tk.Frame):
    """Frame displaying PDF page thumbnails."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Create scrollable frame
        self.canvas = tk.Canvas(self, bg="white")
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Bind mouse wheel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        
        self.thumbnails = []
        self.selected_pages = set()
        self.selection_callback = None
    
    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling."""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def set_selection_callback(self, callback: Callable[[set], None]):
        """Set callback for page selection changes."""
        self.selection_callback = callback
    
    def clear_thumbnails(self):
        """Clear all thumbnails."""
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        self.thumbnails.clear()
        self.selected_pages.clear()
    
    def add_thumbnail(self, page_num: int, image: Image.Image, selectable: bool = True):
        """Add a thumbnail to the frame."""
        # Resize image to thumbnail size
        image.thumbnail((150, 200), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(image)
        
        # Create frame for thumbnail
        thumb_frame = tk.Frame(self.scrollable_frame, relief="raised", bd=1)
        thumb_frame.pack(pady=5, padx=5, fill="x")
        
        # Page number label
        page_label = tk.Label(thumb_frame, text=f"Page {page_num + 1}", font=("Arial", 10, "bold"))
        page_label.pack()
        
        # Thumbnail image
        img_label = tk.Label(thumb_frame, image=photo)
        img_label.image = photo  # Keep a reference
        img_label.pack()
        
        if selectable:
            # Checkbox for selection
            var = tk.BooleanVar()
            checkbox = tk.Checkbutton(thumb_frame, variable=var,
                                    command=lambda: self._on_selection_change(page_num, var.get()))
            checkbox.pack()
            
            self.thumbnails.append({
                'page_num': page_num,
                'frame': thumb_frame,
                'var': var,
                'checkbox': checkbox
            })
    
    def _on_selection_change(self, page_num: int, selected: bool):
        """Handle page selection change."""
        if selected:
            self.selected_pages.add(page_num)
        else:
            self.selected_pages.discard(page_num)
        
        if self.selection_callback:
            self.selection_callback(self.selected_pages)
    
    def select_all(self):
        """Select all pages."""
        for thumb in self.thumbnails:
            thumb['var'].set(True)
            self.selected_pages.add(thumb['page_num'])
        
        if self.selection_callback:
            self.selection_callback(self.selected_pages)
    
    def select_none(self):
        """Deselect all pages."""
        for thumb in self.thumbnails:
            thumb['var'].set(False)
        self.selected_pages.clear()
        
        if self.selection_callback:
            self.selection_callback(self.selected_pages)


class FileListFrame(tk.Frame):
    """Frame for displaying and managing a list of files."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # Create listbox with scrollbar
        self.listbox = tk.Listbox(self, selectmode=tk.EXTENDED)
        self.scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.listbox.yview)
        self.listbox.configure(yscrollcommand=self.scrollbar.set)
        
        self.listbox.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Store file paths
        self.file_paths = []
        
        # Bind events
        self.listbox.bind("<Double-Button-1>", self._on_double_click)
        self.listbox.bind("<<ListboxSelect>>", self._on_selection_change)
        
        self.selection_callback = None
        self.double_click_callback = None
    
    def set_selection_callback(self, callback: Callable[[List[str]], None]):
        """Set callback for selection changes."""
        self.selection_callback = callback
    
    def set_double_click_callback(self, callback: Callable[[str], None]):
        """Set callback for double-click events."""
        self.double_click_callback = callback
    
    def add_file(self, file_path: str):
        """Add a file to the list."""
        import os
        self.file_paths.append(file_path)
        self.listbox.insert(tk.END, os.path.basename(file_path))
    
    def add_files(self, file_paths: List[str]):
        """Add multiple files to the list."""
        for file_path in file_paths:
            self.add_file(file_path)
    
    def remove_selected(self):
        """Remove selected files from the list."""
        selected_indices = list(self.listbox.curselection())
        selected_indices.reverse()  # Remove from end to avoid index shifting
        
        for index in selected_indices:
            self.listbox.delete(index)
            del self.file_paths[index]
    
    def clear_all(self):
        """Clear all files from the list."""
        self.listbox.delete(0, tk.END)
        self.file_paths.clear()
    
    def get_selected_files(self) -> List[str]:
        """Get list of selected file paths."""
        selected_indices = self.listbox.curselection()
        return [self.file_paths[i] for i in selected_indices]
    
    def get_all_files(self) -> List[str]:
        """Get list of all file paths."""
        return self.file_paths.copy()
    
    def move_up(self):
        """Move selected items up in the list."""
        selected_indices = list(self.listbox.curselection())
        if not selected_indices or selected_indices[0] == 0:
            return
        
        for i in selected_indices:
            # Swap in listbox
            item = self.listbox.get(i)
            self.listbox.delete(i)
            self.listbox.insert(i - 1, item)
            
            # Swap in file_paths
            self.file_paths[i], self.file_paths[i - 1] = self.file_paths[i - 1], self.file_paths[i]
            
            # Update selection
            self.listbox.selection_set(i - 1)
    
    def move_down(self):
        """Move selected items down in the list."""
        selected_indices = list(self.listbox.curselection())
        if not selected_indices or selected_indices[-1] == self.listbox.size() - 1:
            return
        
        selected_indices.reverse()  # Process from bottom to top
        for i in selected_indices:
            # Swap in listbox
            item = self.listbox.get(i)
            self.listbox.delete(i)
            self.listbox.insert(i + 1, item)
            
            # Swap in file_paths
            self.file_paths[i], self.file_paths[i + 1] = self.file_paths[i + 1], self.file_paths[i]
            
            # Update selection
            self.listbox.selection_set(i + 1)
    
    def _on_selection_change(self, event):
        """Handle selection change."""
        if self.selection_callback:
            self.selection_callback(self.get_selected_files())
    
    def _on_double_click(self, event):
        """Handle double-click event."""
        selection = self.listbox.curselection()
        if selection and self.double_click_callback:
            file_path = self.file_paths[selection[0]]
            self.double_click_callback(file_path)
