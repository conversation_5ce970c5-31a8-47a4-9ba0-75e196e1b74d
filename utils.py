"""
Utility functions for the PDF manipulation application.
"""

import os
import tkinter as tk
from tkinter import filedialog, messagebox
from typing import List, Optional, Tuple
import fitz  # PyMuPDF


def validate_pdf_file(file_path: str) -> bool:
    """
    Validate if a file is a valid PDF.

    Args:
        file_path (str): Path to the file to validate

    Returns:
        bool: True if valid PDF, False otherwise
    """
    try:
        doc = fitz.open(file_path)
        doc.close()
        return True
    except Exception:
        return False


def get_pdf_info(file_path: str) -> Optional[dict]:
    """
    Get basic information about a PDF file.

    Args:
        file_path (str): Path to the PDF file

    Returns:
        dict: PDF information or None if error
    """
    try:
        doc = fitz.open(file_path)
        info = {
            'page_count': len(doc),
            'title': doc.metadata.get('title', ''),
            'author': doc.metadata.get('author', ''),
            'subject': doc.metadata.get('subject', ''),
            'creator': doc.metadata.get('creator', ''),
            'producer': doc.metadata.get('producer', ''),
            'creation_date': doc.metadata.get('creationDate', ''),
            'modification_date': doc.metadata.get('modDate', ''),
            'file_size': os.path.getsize(file_path)
        }
        doc.close()
        return info
    except Exception as e:
        print(f"Error getting PDF info: {e}")
        return None


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human readable format.

    Args:
        size_bytes (int): Size in bytes

    Returns:
        str: Formatted size string
    """
    if size_bytes == 0:
        return "0B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1

    return f"{size_bytes:.1f}{size_names[i]}"


def select_output_file(default_name: str = "output.pdf", title: str = "Save PDF As") -> Optional[str]:
    """
    Open file dialog to select output file location.

    Args:
        default_name (str): Default filename
        title (str): Dialog title

    Returns:
        str: Selected file path or None if cancelled
    """
    return filedialog.asksaveasfilename(
        title=title,
        defaultextension=".pdf",
        filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
        initialfile=default_name
    )


def select_input_files(title: str = "Select PDF Files") -> List[str]:
    """
    Open file dialog to select multiple input PDF files.

    Args:
        title (str): Dialog title

    Returns:
        List[str]: List of selected file paths
    """
    return list(filedialog.askopenfilenames(
        title=title,
        filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
    ))


def show_error(title: str, message: str):
    """Show error message dialog."""
    messagebox.showerror(title, message)


def show_info(title: str, message: str):
    """Show info message dialog."""
    messagebox.showinfo(title, message)


def show_warning(title: str, message: str):
    """Show warning message dialog."""
    messagebox.showwarning(title, message)


def ask_yes_no(title: str, message: str) -> bool:
    """
    Show yes/no question dialog.

    Returns:
        bool: True if yes, False if no
    """
    return messagebox.askyesno(title, message)


def center_window(window: tk.Tk, width: int, height: int):
    """
    Center a window on the screen.

    Args:
        window: Tkinter window
        width: Window width
        height: Window height
    """
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()

    x = (screen_width - width) // 2
    y = (screen_height - height) // 2

    window.geometry(f"{width}x{height}+{x}+{y}")


def parse_page_range(page_range: str, max_pages: int) -> List[int]:
    """
    Parse page range string into list of page numbers.

    Args:
        page_range (str): Page range (e.g., "1-3,5,7-9")
        max_pages (int): Maximum number of pages

    Returns:
        List[int]: List of page numbers (0-indexed)
    """
    pages = []

    try:
        for part in page_range.split(','):
            part = part.strip()
            if '-' in part:
                start, end = part.split('-', 1)
                start = int(start.strip()) - 1  # Convert to 0-indexed
                end = int(end.strip()) - 1      # Convert to 0-indexed

                # Validate range
                start = max(0, min(start, max_pages - 1))
                end = max(0, min(end, max_pages - 1))

                if start <= end:
                    pages.extend(range(start, end + 1))
            else:
                page_num = int(part) - 1  # Convert to 0-indexed
                if 0 <= page_num < max_pages:
                    pages.append(page_num)
    except ValueError:
        raise ValueError("Invalid page range format")

    return sorted(list(set(pages)))  # Remove duplicates and sort
