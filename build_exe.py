#!/usr/bin/env python3
"""
PDF Manipulator Pro - Executable Builder
Creates a standalone executable using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed."""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """Install PyInstaller if not present."""
    print("📦 Installing PyInstaller...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                      check=True, capture_output=True)
        print("✅ PyInstaller installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyInstaller: {e}")
        return False

def clean_build_dirs():
    """Clean previous build directories."""
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 Cleaning {dir_name}/")
            shutil.rmtree(dir_name)

def build_executable():
    """Build the executable using PyInstaller."""
    print("🔨 Building PDF Manipulator Pro executable...")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Single executable file
        "--windowed",                   # No console window
        "--name=PDF_Manipulator_Pro",   # Executable name
        "--icon=icon.ico",              # Application icon
        "--add-data=icon.ico;.",        # Include icon in bundle
        "--hidden-import=tkinter",      # Ensure tkinter is included
        "--hidden-import=tkinter.ttk",  # Include ttk
        "--hidden-import=tkinter.filedialog",
        "--hidden-import=tkinter.messagebox",
        "--hidden-import=PIL",
        "--hidden-import=PIL.Image",
        "--hidden-import=PIL.ImageTk",
        "--hidden-import=fitz",
        "--hidden-import=PyPDF2",
        "--hidden-import=tkinterdnd2",
        "pdf_manipulator_pro.py"       # Main script
    ]
    
    try:
        print("⚙️ Running PyInstaller...")
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def create_portable_package():
    """Create a portable package with the executable."""
    if not os.path.exists("dist/PDF_Manipulator_Pro.exe"):
        print("❌ Executable not found in dist/")
        return False
    
    # Create portable directory
    portable_dir = "PDF_Manipulator_Pro_Portable"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir)
    
    # Copy executable
    shutil.copy2("dist/PDF_Manipulator_Pro.exe", portable_dir)
    
    # Copy icon
    if os.path.exists("icon.ico"):
        shutil.copy2("icon.ico", portable_dir)
    
    # Create README for portable version
    readme_content = """# PDF Manipulator Pro - Portable Version

## How to Use
1. Double-click PDF_Manipulator_Pro.exe to start the application
2. The application will launch directly to the main interface
3. Use the tabs to access different PDF manipulation tools

## Features
- Merge multiple PDF files
- Split PDFs by pages or ranges
- Extract specific pages
- Rotate pages (90°, 180°, 270°)
- Delete unwanted pages
- Create PDFs from text or images

## System Requirements
- Windows 10/11
- No additional software installation required

## Support
This is a standalone executable that includes all required dependencies.
No Python installation or additional packages are needed.
"""
    
    with open(f"{portable_dir}/README.txt", "w") as f:
        f.write(readme_content)
    
    print(f"📦 Portable package created: {portable_dir}/")
    return True

def main():
    """Main build process."""
    print("🚀 PDF Manipulator Pro - Executable Builder")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("pdf_manipulator_pro.py"):
        print("❌ Error: pdf_manipulator_pro.py not found!")
        print("Please run this script from the project directory.")
        return 1
    
    # Check PyInstaller
    if not check_pyinstaller():
        print("PyInstaller not found. Installing...")
        if not install_pyinstaller():
            return 1
    
    # Clean previous builds
    clean_build_dirs()
    
    # Build executable
    if not build_executable():
        return 1
    
    # Create portable package
    if not create_portable_package():
        return 1
    
    print("\n🎉 Build Process Complete!")
    print("=" * 50)
    print("📁 Executable location: dist/PDF_Manipulator_Pro.exe")
    print("📦 Portable package: PDF_Manipulator_Pro_Portable/")
    print("\n✅ Ready to distribute!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
