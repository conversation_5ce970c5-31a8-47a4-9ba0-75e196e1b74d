"""
Build script for creating Windows executable using PyInstaller.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def build_executable(modern=False):
    """Build the executable using PyInstaller."""

    # Define paths
    script_dir = Path(__file__).parent
    main_script = script_dir / ("modern_main.py" if modern else "main.py")
    dist_dir = script_dir / "dist"
    build_dir = script_dir / "build"

    # Clean previous builds
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    if build_dir.exists():
        shutil.rmtree(build_dir)

    # PyInstaller command
    app_name = "PDFManipulator-Modern" if modern else "PDFManipulator"
    cmd = [
        "pyinstaller",
        "--onefile",                    # Create a single executable file
        "--windowed",                   # Don't show console window
        f"--name={app_name}",           # Name of the executable
        "--icon=icon.ico",              # Icon file (if available)
        "--add-data=requirements.txt;.", # Include requirements file
        "--hidden-import=tkinterdnd2",  # Ensure tkinterdnd2 is included
        "--hidden-import=PIL",          # Ensure PIL is included
        "--hidden-import=fitz",         # Ensure PyMuPDF is included
        "--hidden-import=customtkinter", # Ensure CustomTkinter is included
        "--collect-all=tkinterdnd2",    # Collect all tkinterdnd2 files
        "--collect-all=PIL",            # Collect all PIL files
        "--collect-all=fitz",           # Collect all PyMuPDF files
        "--collect-all=customtkinter",  # Collect all CustomTkinter files
        str(main_script)
    ]

    # Remove icon parameter if icon file doesn't exist
    icon_path = script_dir / "icon.ico"
    if not icon_path.exists():
        cmd = [arg for arg in cmd if not arg.startswith("--icon")]

    print("Building executable...")
    print(f"Command: {' '.join(cmd)}")

    try:
        # Run PyInstaller
        result = subprocess.run(cmd, cwd=script_dir, capture_output=True, text=True)

        if result.returncode == 0:
            print("Build successful!")
            exe_name = f"{app_name}.exe"
            print(f"Executable created: {dist_dir / exe_name}")

            # Show file size
            exe_path = dist_dir / exe_name
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"File size: {size_mb:.1f} MB")
        else:
            print("Build failed!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False

    except FileNotFoundError:
        print("Error: PyInstaller not found. Please install it with: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"Error during build: {e}")
        return False

    return True


def create_installer_script():
    """Create a simple batch script for easy installation."""

    script_content = """@echo off
echo PDF Manipulator Installer
echo ========================

echo.
echo Installing Python dependencies...
pip install -r requirements.txt

echo.
echo Building executable...
python build_exe.py

echo.
echo Build complete!
echo The executable can be found in the 'dist' folder.
echo.
pause
"""

    with open("install.bat", "w") as f:
        f.write(script_content)

    print("Created install.bat script")


def main():
    """Main function."""
    print("PDF Manipulator Build Script")
    print("============================")

    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("Error: main.py not found. Please run this script from the project directory.")
        return

    # Check if requirements.txt exists
    if not Path("requirements.txt").exists():
        print("Error: requirements.txt not found.")
        return

    # Create installer script
    create_installer_script()

    # Ask user which version to build
    print("\nWhich version would you like to build?")
    print("1. Classic UI (main.py)")
    print("2. Modern UI (modern_main.py)")
    print("3. Both versions")

    choice = input("\nEnter your choice (1/2/3): ").strip()

    if choice == "1":
        versions = [False]
        version_names = ["Classic"]
    elif choice == "2":
        versions = [True]
        version_names = ["Modern"]
    elif choice == "3":
        versions = [False, True]
        version_names = ["Classic", "Modern"]
    else:
        print("Invalid choice. Exiting.")
        return

    # Ask user if they want to build now
    response = input(f"\nDo you want to build the {'/'.join(version_names)} version(s) now? (y/n): ").lower().strip()

    if response in ['y', 'yes']:
        all_success = True

        for i, modern in enumerate(versions):
            print(f"\n{'='*50}")
            print(f"Building {version_names[i]} Version...")
            print(f"{'='*50}")

            success = build_executable(modern)
            if not success:
                all_success = False

        if all_success:
            print("\n" + "="*50)
            print("BUILD SUCCESSFUL!")
            print("="*50)
            print("The executable(s) have been created in the 'dist' folder.")
            if len(versions) == 1:
                exe_name = "PDFManipulator-Modern.exe" if versions[0] else "PDFManipulator.exe"
                print(f"You can distribute the {exe_name} file to other Windows computers.")
            else:
                print("You can distribute both PDFManipulator.exe and PDFManipulator-Modern.exe files.")
            print("No Python installation is required on the target machines.")
        else:
            print("\n" + "="*50)
            print("BUILD FAILED!")
            print("="*50)
            print("Please check the error messages above and try again.")
    else:
        print("\nBuild skipped. You can run this script again later to build the executable.")
        print("Or use the install.bat script for automated installation and building.")


if __name__ == "__main__":
    main()
