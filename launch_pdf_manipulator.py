#!/usr/bin/env python3
"""
PDF Manipulator Pro Launcher
Simple launcher script for the PDF manipulation application.
"""

import sys
import os

def main():
    """Launch the PDF Manipulator Pro application."""
    try:
        # Import the main application
        from modern_main import ModernPDFManipulatorApp
        
        # Create and run the application with welcome screen
        print("Starting PDF Manipulator Pro...")
        app = ModernPDFManipulatorApp(show_welcome=True)
        app.run()
        
    except ImportError as e:
        print(f"Error: Missing required modules. {e}")
        print("Please install required packages:")
        print("pip install PyPDF2 PyMuPDF pillow tkinterdnd2")
        sys.exit(1)
        
    except Exception as e:
        print(f"Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
