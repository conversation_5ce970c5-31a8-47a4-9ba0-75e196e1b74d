"""
Simple PDF Manipulation Desktop Application
A basic GUI application for PDF processing operations using standard tkinter.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
from typing import List, Optional

# Try to import tkinterdnd2, but don't fail if it's not available
try:
    from tkinterdnd2 import TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    print("Warning: tkinterdnd2 not available - drag & drop will be disabled")
    TkinterDnD = tk
    DRAG_DROP_AVAILABLE = False

# Import our custom modules
from pdf_processor import PDFProcessor
from utils import (
    validate_pdf_file, get_pdf_info, format_file_size,
    select_output_file, show_error, show_info, center_window
)


class SimplePDFManipulatorApp:
    """Simple main application class for PDF manipulation."""

    def __init__(self, show_welcome=False):
        # Initialize main window
        if DRAG_DROP_AVAILABLE:
            self.root = TkinterDnD.Tk()
        else:
            self.root = tk.Tk()
            
        self.root.title("PDF Manipulator Pro - Simple PDF Processing")
        self.root.geometry("1000x700")

        # Set window icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Center window
        center_window(self.root, 1000, 700)

        # Set appearance
        self.root.configure(bg='#f0f0f0')

        # Initialize PDF processor
        self.pdf_processor = PDFProcessor()

        # Initialize variables
        self.merge_files = []
        self.current_pdf_path = ""

        # Create interface
        self._create_main_interface()

        # Update status
        self._update_status("Ready - Select an operation to get started")

    def _create_main_interface(self):
        """Create the simple interface with notebook tabs."""
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)

        # Create tabs
        self._create_merge_tab()
        self._create_split_tab()
        self._create_extract_tab()
        self._create_rotate_tab()

        # Status bar
        self.status_frame = ttk.Frame(main_frame)
        self.status_frame.pack(fill="x", pady=(10, 0))

        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready - Select an operation to get started",
            relief="sunken",
            anchor="w"
        )
        self.status_label.pack(fill="x", padx=5, pady=5)

    def _create_merge_tab(self):
        """Create the merge PDFs tab."""
        merge_frame = ttk.Frame(self.notebook)
        self.notebook.add(merge_frame, text="🔄 Merge PDFs")

        # Left panel - file list
        left_frame = ttk.LabelFrame(merge_frame, text="PDF Files to Merge")
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5), pady=5)

        # File listbox
        self.merge_listbox = tk.Listbox(left_frame, selectmode=tk.EXTENDED)
        self.merge_listbox.pack(fill="both", expand=True, padx=5, pady=5)

        # File control buttons
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(btn_frame, text="Add Files", command=self._add_merge_files).pack(side="left", padx=2)
        ttk.Button(btn_frame, text="Remove", command=self._remove_merge_files).pack(side="left", padx=2)
        ttk.Button(btn_frame, text="Clear All", command=self._clear_merge_files).pack(side="left", padx=2)

        # Right panel - options
        right_frame = ttk.LabelFrame(merge_frame, text="Merge Options")
        right_frame.pack(side="right", fill="y", padx=(5, 0), pady=5)

        # Output file selection
        ttk.Label(right_frame, text="Output File:").pack(anchor="w", padx=5, pady=(5, 0))
        
        output_frame = ttk.Frame(right_frame)
        output_frame.pack(fill="x", padx=5, pady=5)
        
        self.merge_output_var = tk.StringVar()
        self.merge_output_entry = ttk.Entry(output_frame, textvariable=self.merge_output_var, state="readonly")
        self.merge_output_entry.pack(side="left", fill="x", expand=True)
        
        ttk.Button(output_frame, text="Browse", command=self._select_merge_output).pack(side="right", padx=(5, 0))

        # Merge button
        ttk.Button(right_frame, text="Merge PDFs", command=self._merge_pdfs).pack(fill="x", padx=5, pady=10)

        # Info text
        info_frame = ttk.LabelFrame(right_frame, text="File Information")
        info_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        self.merge_info_text = tk.Text(info_frame, height=8, width=30, wrap=tk.WORD)
        self.merge_info_text.pack(fill="both", expand=True, padx=5, pady=5)

    def _create_split_tab(self):
        """Create the split PDF tab."""
        split_frame = ttk.Frame(self.notebook)
        self.notebook.add(split_frame, text="✂️ Split PDF")

        # File selection
        file_frame = ttk.LabelFrame(split_frame, text="Select PDF File")
        file_frame.pack(fill="x", padx=5, pady=5)

        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill="x", padx=5, pady=5)

        self.split_input_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.split_input_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(input_frame, text="Browse", command=self._select_split_input).pack(side="right", padx=(5, 0))

        # Split options
        options_frame = ttk.LabelFrame(split_frame, text="Split Options")
        options_frame.pack(fill="x", padx=5, pady=5)

        self.split_method_var = tk.StringVar(value="pages")
        ttk.Radiobutton(options_frame, text="Split by page range", variable=self.split_method_var, value="pages").pack(anchor="w", padx=5)
        ttk.Radiobutton(options_frame, text="Split each page", variable=self.split_method_var, value="each").pack(anchor="w", padx=5)

        # Page range
        range_frame = ttk.Frame(options_frame)
        range_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(range_frame, text="Page range (e.g., 1-5):").pack(side="left")
        self.split_range_var = tk.StringVar(value="1-")
        ttk.Entry(range_frame, textvariable=self.split_range_var, width=15).pack(side="right")

        # Output directory
        output_frame = ttk.LabelFrame(split_frame, text="Output Directory")
        output_frame.pack(fill="x", padx=5, pady=5)

        dir_frame = ttk.Frame(output_frame)
        dir_frame.pack(fill="x", padx=5, pady=5)

        self.split_output_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.split_output_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(dir_frame, text="Browse", command=self._select_split_output).pack(side="right", padx=(5, 0))

        # Split button
        ttk.Button(split_frame, text="Split PDF", command=self._split_pdf).pack(pady=10)

    def _create_extract_tab(self):
        """Create the extract pages tab."""
        extract_frame = ttk.Frame(self.notebook)
        self.notebook.add(extract_frame, text="📄 Extract Pages")

        # File selection
        file_frame = ttk.LabelFrame(extract_frame, text="Select PDF File")
        file_frame.pack(fill="x", padx=5, pady=5)

        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill="x", padx=5, pady=5)

        self.extract_input_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.extract_input_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(input_frame, text="Browse", command=self._select_extract_input).pack(side="right", padx=(5, 0))

        # Page selection
        pages_frame = ttk.LabelFrame(extract_frame, text="Pages to Extract")
        pages_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(pages_frame, text="Page numbers (e.g., 1,3,5-7):").pack(anchor="w", padx=5, pady=2)
        self.extract_pages_var = tk.StringVar()
        ttk.Entry(pages_frame, textvariable=self.extract_pages_var).pack(fill="x", padx=5, pady=5)

        # Output file
        output_frame = ttk.LabelFrame(extract_frame, text="Output File")
        output_frame.pack(fill="x", padx=5, pady=5)

        out_frame = ttk.Frame(output_frame)
        out_frame.pack(fill="x", padx=5, pady=5)

        self.extract_output_var = tk.StringVar()
        ttk.Entry(out_frame, textvariable=self.extract_output_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(out_frame, text="Browse", command=self._select_extract_output).pack(side="right", padx=(5, 0))

        # Extract button
        ttk.Button(extract_frame, text="Extract Pages", command=self._extract_pages).pack(pady=10)

    def _create_rotate_tab(self):
        """Create the rotate pages tab."""
        rotate_frame = ttk.Frame(self.notebook)
        self.notebook.add(rotate_frame, text="🔄 Rotate Pages")

        # File selection
        file_frame = ttk.LabelFrame(rotate_frame, text="Select PDF File")
        file_frame.pack(fill="x", padx=5, pady=5)

        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill="x", padx=5, pady=5)

        self.rotate_input_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.rotate_input_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(input_frame, text="Browse", command=self._select_rotate_input).pack(side="right", padx=(5, 0))

        # Rotation options
        options_frame = ttk.LabelFrame(rotate_frame, text="Rotation Options")
        options_frame.pack(fill="x", padx=5, pady=5)

        self.rotate_angle_var = tk.StringVar(value="90")
        ttk.Radiobutton(options_frame, text="90° Clockwise", variable=self.rotate_angle_var, value="90").pack(anchor="w", padx=5)
        ttk.Radiobutton(options_frame, text="180°", variable=self.rotate_angle_var, value="180").pack(anchor="w", padx=5)
        ttk.Radiobutton(options_frame, text="270° (90° Counter-clockwise)", variable=self.rotate_angle_var, value="270").pack(anchor="w", padx=5)

        # Page selection
        pages_frame = ttk.LabelFrame(rotate_frame, text="Pages to Rotate")
        pages_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(pages_frame, text="Page numbers (e.g., 1,3,5-7 or 'all'):").pack(anchor="w", padx=5, pady=2)
        self.rotate_pages_var = tk.StringVar(value="all")
        ttk.Entry(pages_frame, textvariable=self.rotate_pages_var).pack(fill="x", padx=5, pady=5)

        # Output file
        output_frame = ttk.LabelFrame(rotate_frame, text="Output File")
        output_frame.pack(fill="x", padx=5, pady=5)

        out_frame = ttk.Frame(output_frame)
        out_frame.pack(fill="x", padx=5, pady=5)

        self.rotate_output_var = tk.StringVar()
        ttk.Entry(out_frame, textvariable=self.rotate_output_var, state="readonly").pack(side="left", fill="x", expand=True)
        ttk.Button(out_frame, text="Browse", command=self._select_rotate_output).pack(side="right", padx=(5, 0))

        # Rotate button
        ttk.Button(rotate_frame, text="Rotate Pages", command=self._rotate_pages).pack(pady=10)

    def _update_status(self, message, status_type="info"):
        """Update status bar message."""
        if status_type == "error":
            message = f"❌ {message}"
        elif status_type == "success":
            message = f"✅ {message}"
        elif status_type == "processing":
            message = f"⏳ {message}"
        else:
            message = f"🟢 {message}"
        
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def run(self):
        """Start the application."""
        self.root.mainloop()

    # Placeholder methods for functionality
    def _add_merge_files(self):
        files = filedialog.askopenfilenames(
            title="Select PDF files to merge",
            filetypes=[("PDF files", "*.pdf")]
        )
        for file in files:
            if validate_pdf_file(file):
                self.merge_listbox.insert(tk.END, os.path.basename(file))
                self.merge_files.append(file)
        self._update_merge_info()

    def _remove_merge_files(self):
        selected = self.merge_listbox.curselection()
        for i in reversed(selected):
            self.merge_listbox.delete(i)
            del self.merge_files[i]
        self._update_merge_info()

    def _clear_merge_files(self):
        self.merge_listbox.delete(0, tk.END)
        self.merge_files.clear()
        self._update_merge_info()

    def _update_merge_info(self):
        info = f"Files selected: {len(self.merge_files)}\n\n"
        for i, file in enumerate(self.merge_files, 1):
            info += f"{i}. {os.path.basename(file)}\n"
        
        self.merge_info_text.delete(1.0, tk.END)
        self.merge_info_text.insert(1.0, info)

    def _select_merge_output(self):
        file = filedialog.asksaveasfilename(
            title="Save merged PDF as",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")]
        )
        if file:
            self.merge_output_var.set(file)

    def _merge_pdfs(self):
        if not self.merge_files:
            show_error("No Files", "Please add PDF files to merge.")
            return
        
        output_path = self.merge_output_var.get()
        if not output_path:
            show_error("No Output", "Please select an output file.")
            return

        try:
            self._update_status("Merging PDFs...", "processing")
            success = self.pdf_processor.merge_pdfs(self.merge_files, output_path)
            
            if success:
                self._update_status("PDFs merged successfully!", "success")
                show_info("Success", f"PDFs merged successfully!\nSaved to: {output_path}")
            else:
                self._update_status("Failed to merge PDFs", "error")
                show_error("Error", "Failed to merge PDFs. Please check the files and try again.")
        except Exception as e:
            self._update_status("Error during merge", "error")
            show_error("Error", f"An error occurred: {str(e)}")

    # Placeholder methods for other operations
    def _select_split_input(self):
        file = filedialog.askopenfilename(title="Select PDF to split", filetypes=[("PDF files", "*.pdf")])
        if file and validate_pdf_file(file):
            self.split_input_var.set(file)

    def _select_split_output(self):
        directory = filedialog.askdirectory(title="Select output directory")
        if directory:
            self.split_output_var.set(directory)

    def _split_pdf(self):
        show_info("Feature", "Split PDF functionality will be implemented in the next update.")

    def _select_extract_input(self):
        file = filedialog.askopenfilename(title="Select PDF to extract from", filetypes=[("PDF files", "*.pdf")])
        if file and validate_pdf_file(file):
            self.extract_input_var.set(file)

    def _select_extract_output(self):
        file = filedialog.asksaveasfilename(title="Save extracted pages as", defaultextension=".pdf", filetypes=[("PDF files", "*.pdf")])
        if file:
            self.extract_output_var.set(file)

    def _extract_pages(self):
        show_info("Feature", "Extract pages functionality will be implemented in the next update.")

    def _select_rotate_input(self):
        file = filedialog.askopenfilename(title="Select PDF to rotate", filetypes=[("PDF files", "*.pdf")])
        if file and validate_pdf_file(file):
            self.rotate_input_var.set(file)

    def _select_rotate_output(self):
        file = filedialog.asksaveasfilename(title="Save rotated PDF as", defaultextension=".pdf", filetypes=[("PDF files", "*.pdf")])
        if file:
            self.rotate_output_var.set(file)

    def _rotate_pages(self):
        show_info("Feature", "Rotate pages functionality will be implemented in the next update.")


if __name__ == "__main__":
    app = SimplePDFManipulatorApp()
    app.run()
